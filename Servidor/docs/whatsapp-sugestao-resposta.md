# Documentação da Rota `/whatsapp/sugestao-resposta`

## Visão Geral

A rota `/whatsapp/sugestao-resposta` é um endpoint de inteligência artificial que auxilia vendedores (SDRs) a gerar respostas efetivas no WhatsApp usando a metodologia SPIN Selling. O sistema utiliza OpenAI GPT-4 com Function Calling para criar sugestões personalizadas baseadas no contexto da conversa.

## Endpoint

```
POST /api/whatsapp/sugestao-resposta
```

## Request Body

```typescript
{
  telefone: string;           // Obrigatório - Telefone do lead
  mensagemSdr: string;        // Obrigatório - Mensagem do vendedor
  mensagens?: Array<{         // Histórico de mensagens do lead
    texto: string;
    remetente: string;
    fromMe?: boolean;
  }>;
  faseSpin?: 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto';
  produto?: string;           // Default: 'Meu <PERSON>'
  tomConversa?: 'formal' | 'informal' | 'tecnico';
  historicoConversaAgente?: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp?: Date;
  }>;
  contato?: {                 // Dados do lead
    nomeResponsavel?: string;
    empresa?: string;
    email?: string;
    telefone?: string;
    instagramHandle?: string;
    bioInsta?: string;
    concorrente?: string;
    segmento?: string;
    // ... outros campos do lead
  };
}
```

## Response

```typescript
{
  sucesso: boolean;
  data?: {
    resposta: string;         // Texto da sugestão gerada
    confianca: number;        // Nível de confiança (0-1)
    faseSugerida?: string;    // Fase SPIN detectada/sugerida
    observacoes?: string;     // Observações adicionais
  };
  erro?: string;              // Mensagem de erro se falhar
  detalhes?: string;          // Detalhes do erro
}
```

## Fluxo de Processamento

### 1. Validação de Entrada
- Verifica se telefone foi fornecido
- Verifica se mensagemSdr foi fornecida
- Extrai empresaId do contexto da requisição

### 2. Busca de Dados do Lead
```typescript
const mapeadorLead = new MapeadorDeLead();
const lead = await mapeadorLead.selecioneSync({ telefone });
```

### 3. Detecção de Fase SPIN
Se `faseSpin` é 'auto' ou não fornecida:
- Analisa histórico de mensagens
- Usa IA para detectar automaticamente a fase atual
- Considera se o lead ainda não respondeu (força fase 'rapport')

### 4. Geração de Resposta com IA
```typescript
const assistantService = new CrmWhatsappAssistantService();
const resposta = await assistantService.conversarComAgente({
  telefone,
  mensagemSdr,
  mensagensLead,
  faseSpin,
  produto,
  tomConversa,
  contato,
  historicoConversaAgente
});
```

### 5. Function Calling Loop
O sistema pode chamar automaticamente as seguintes funções:

#### `buscar_info_concorrente`
- Busca informações sobre concorrentes (iFood, Uber Eats, etc.)
- Retorna taxas, limitações e vantagens comparativas

#### `buscar_sobre_meu_cardapio`
- Obtém benefícios do Meu Cardápio por categoria
- Categorias: delivery, fidelidade, automação, gestão, marketing

#### `consultar_dados_lead`
- Busca dados enriquecidos do lead
- Retorna insights e recomendações de abordagem

#### `analisar_perfil_instagram`
- Analisa perfil do Instagram do lead
- Identifica oportunidades de vendas

#### `buscar_caso_sucesso`
- Busca cases de sucesso similares
- Filtra por segmento, tamanho, região e problema

#### `analisar_objecao`
- Fornece estratégias para lidar com objeções
- Tipos: preço, mudança, tempo, confiança, funcionalidade, decisão

#### `gerar_rapport_personalizado`
- Gera estratégias de rapport personalizadas
- Considera tentativas anteriores e perfil do lead

## Metodologia SPIN Selling

### Fases da Venda

1. **Rapport** (Lead não respondeu)
   - Foco em quebrar o gelo
   - Elogios sinceros baseados em dados reais
   - Sem mencionar produto ou vendas

2. **Situação**
   - Perguntas exploratórias sobre o negócio
   - Entender processos atuais
   - Tom neutro e investigativo

3. **Problema**
   - Identificar dificuldades e dores
   - Perguntas que expõem problemas operacionais
   - Demonstrar empatia sem vender

4. **Implicação**
   - Quantificar impactos dos problemas
   - Explorar perdas de tempo/dinheiro
   - Usar dados comparativos do mercado

5. **Necessidade**
   - Conectar dores às soluções
   - Apresentar benefícios específicos
   - Call-to-action para agendar demonstração

## Prompt Engineering

### Estrutura do Prompt
```
1. Contexto do agente (SDR do Meu Cardápio)
2. Metodologia SPIN detalhada
3. Regras de balanceamento 30/70
4. Dados completos do lead
5. Histórico da conversa
6. Instruções específicas da fase
7. Formato de resposta esperado
```

### Regras Importantes
- Mensagens curtas (2-3 linhas para WhatsApp)
- Sem assinaturas formais
- Máximo 2 emojis por mensagem
- Elogios apenas baseados em dados reais
- Foco em obter resposta do lead

## Configurações

### Tom de Conversa
- **formal**: Abordagem profissional
- **informal**: Abordagem descontraída
- **tecnico**: Foco em especificações

### Tipo de Abordagem (Rapport)
- **direta**: Valor e urgência
- **indireta**: Curiosidade e conexão
- **consultiva**: Diagnóstico e consultoria

## Tratamento de Erros

1. **Lead não encontrado**: Continua com dados básicos
2. **Erro na IA**: Retorna sugestões fallback pré-definidas
3. **Timeout**: Retorna erro 500 com detalhes
4. **Function call falha**: Continua sem a informação adicional

## Exemplo de Uso

```javascript
// Request
POST /api/whatsapp/sugestao-resposta
{
  "telefone": "11999999999",
  "mensagemSdr": "Como posso ajudar com a gestão do seu restaurante?",
  "mensagens": [
    {
      "texto": "Oi, vi seu anúncio",
      "remetente": "Cliente"
    }
  ],
  "faseSpin": "auto",
  "produto": "Meu Cardápio",
  "tomConversa": "informal"
}

// Response
{
  "sucesso": true,
  "data": {
    "resposta": "Oi! Que legal que você se interessou! 😊 Vi que vocês têm um restaurante incrível. Como vocês gerenciam os pedidos do delivery hoje?",
    "confianca": 0.9,
    "faseSugerida": "situacao",
    "observacoes": "Fase detectada: situacao. Lead demonstrou interesse inicial."
  }
}
```

## Logs e Monitoramento

O sistema registra:
- Todas as requisições recebidas
- Detecção de fase SPIN
- Chamadas de funções executadas
- Respostas geradas
- Tempo de processamento
- Erros e exceções

## Performance

- Tempo médio de resposta: 2-5 segundos
- Máximo de iterações function calling: 10
- Timeout da API: 30 segundos
- Cache de benefícios do Meu Cardápio

## Segurança

- Validação de entrada obrigatória
- Sanitização de dados do lead
- API keys armazenadas como variáveis de ambiente
- Rate limiting por telefone

## Melhorias Futuras

1. Cache de respostas por contexto similar
2. Analytics de efetividade das sugestões
3. Personalização por vendedor
4. Integração com CRM para atualização automática
5. Suporte a múltiplos idiomas