import {Carta<PERSON>} from '../domain/Cartao';
import {MapeadorDeCartao} from '../mapeadores/MapeadorDeCartao';
import {ContatoService} from './ContatoService';
import {NotificacaoService} from './NotificacaoService';
import {Ambiente} from './Ambiente';
import {MapeadorDeContato} from "../mapeadores/MapeadorDeContato";
import {PontuacaoRegistrada} from "../domain/PontuacaoRegistrada";
import {TipoDePontuacao} from "../domain/TipoDePontuacao";
import {Contato} from "../domain/Contato";
import {AcaoDoContato} from "../domain/AcaoDoContato";
import {MapeadorDeAcaoDoContato} from "../mapeadores/MapeadorDeAcaoDoContato";
import {MapeadorDePontuacaoRegistrada} from "../mapeadores/MapeadorDePontuacaoRegistrada";
import {MapeadorDeBrinde} from "../mapeadores/MapeadorDeBrinde";
import {Resposta} from "../utils/Resposta";
import {Brinde} from "../domain/obj/Brinde";
import {BrindeResgatado} from "../domain/BrindeResgatado";
import {MapeadorDeBrindeResgatado} from "../mapeadores/MapeadorDeBrindeResgatado";
import {EnumStatusContato} from "../lib/emun/EnumStatusContato";
// @ts-ignore
import async = require('async');
const uuidv1 = require('uuid/v1');
// @ts-ignore
import _ = require('underscore');
// @ts-ignore
import moment = require("moment");
import {MapeadorDeNotificacao} from "../mapeadores/MapeadorDeNotificacao";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {EmpresaContatosPontuado} from "../domain/utils/EmpresaContatosPontuado";
import {Pedido} from "../domain/delivery/Pedido";
import {Empresa} from "../domain/Empresa";
import {FormatadorUtils} from "../lib/FormatadorUtils";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";
import {PontuacaoEstornada} from "../domain/historico/PontuacaoEstornada";
import {PedidoGenerico} from "../domain/delivery/PedidoGenerico";
const randtoken = require('rand-token');

export class CartaoService {
  private valide(cartao: Cartao, cb: Function)  {
    if ( !cartao ) {
      return cb('Nenhum cartão fidelidade informado.');
    }

    if (!cartao.contato || !cartao.contato.id ) {
      return cb('Nenhum contato informado.');
    }

    if (!cartao.plano || !cartao.plano.id ) {
      return cb('Nenhum plano informado.');
    }

    new MapeadorDeCartao().existe({idContato: cartao.contato.id, idPlano: cartao.plano.id}, (existe: any) => {
      if ( existe ) { return cb('Já existe um cartão ativo nesse plano para esse número de WhatsApp'); }

     // new MapeadorDeEmpresa().obtenhaLimiteContatos(cartao.empresa).then( (empresaLimite: EmpresaContatosPontuado) => {
      //  if(empresaLimite && empresaLimite.qtdeContratado < empresaLimite.qtdePontuado){
      //    cb('Seu plano atingiu o limite de contatos contratados: ' +  empresaLimite.qtdeContratado)
      //  } else {
          cb();
       // }
     // })
    });
  }

  salve(cartao: Cartao): Promise<boolean> {
    return this._salve(cartao, true);
  }

  salveSemNotificar(cartao: Cartao): Promise<boolean> {
    return this._salve(cartao, false);
  }

  private _salve(cartao: Cartao, enviarNotificacao: boolean): Promise<boolean> {
    return new Promise<boolean>( (resolve, reject) => {

      const mapeador = new MapeadorDeCartao();

      mapeador.transacao(async  (conexao: any, commit: Function) => {
        async.series([
          (cb: any) => {
            if ( cartao.contato.id ) {
              new ContatoService().atualizeSemTransacao(cartao.contato, cartao.empresa).then(cb).catch(cb);

            } else {
              new ContatoService().salve(cartao.contato).then(cb).catch(cb);
            }
          },
          (cb: any) => {
            this.valide(cartao, cb );
          },

          (cb: any) => {
            mapeador.insiraSync(cartao).then((inseriu: any) => { cb(); });
          },
          (cb: any) => {
            if( enviarNotificacao ) {
              const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(cartao.empresa));

              notificacaoService.envieNotificacaoBemVindo(cartao.contato).then(() => {
                cb();
              });
            } else {
              cb();
            }
          }
        ], ( erro ) => {
          if ( !erro ) {
            commit( () => {
              resolve(null);
            });
          } else {
             conexao.rollback(() => {
              reject( erro);
            });
          }
        });
      });
    });
  }

  async  salvePontuacaoVendaExterna(empresa: Empresa, cpf: string,   numeroPedido: string, valorVenda: number,
                                    horarioVenda: Date, venda: any){

    return new Promise<any>( async (resolve, reject) => {
      if(!empresa.integracaoPedidoFidelidade)
        return reject('Nenhum integração com fidelidade configurada.')

      let jaPontuou = await new MapeadorDePontuacaoRegistrada().existeSync({ referenciaExterna: numeroPedido});

      if(jaPontuou){
         console.log(String(`Venda  ${numeroPedido} ja pontuou`))
         return reject(String(`Venda  "${numeroPedido}" ja pontuou`));
      }

      let integracaoPedidoFidelidade: any =  empresa.integracaoPedidoFidelidade;
      let plano = integracaoPedidoFidelidade.plano;
      let atividade = integracaoPedidoFidelidade.atividade;

      if(plano.atividadeVariavel() && !venda.itens.length)
        return  reject('Venda não possui nenhum item com produto para cálculo da pontuaçao')

      let pontuacaoRegistrada = new PontuacaoRegistrada();
      pontuacaoRegistrada.valor = valorVenda
      pontuacaoRegistrada.atividades =  [atividade]

      if(empresa.integracaoDelivery)
        atividade.nome =  String(`Venda externa: ${empresa.integracaoDelivery.sistema}`)
      else atividade.nome = 'Venda externa'

      let pontosGanho =  plano.tipoDePontuacao.calculePontos(empresa, pontuacaoRegistrada, venda);

      new MapeadorDeContato().transacao( async (conexao: any, commit: any) => {
        let contato: any = await new MapeadorDeContato().selecioneSync({ cpf: cpf});

        if(!contato){
          contato = new Contato(null, null, null, null, null, cpf);
          contato.setToken(empresa, randtoken);
          contato.ultimaVisita = new Date(1900, 0, 1, 0, 0, 0, 0);

          await new MapeadorDeContato().insiraSync(contato)
        }

        let cartao: any =  contato.cartoes.find((item: any) => item.plano.id === plano.id);

        if(!cartao){
          cartao =   new Cartao(null, contato, plano, 0);
          await new MapeadorDeCartao().insiraSync(cartao)
        } else {
          cartao.contato = contato;
        }

        cartao.empresa = empresa;

        pontuacaoRegistrada.cartao = cartao;
        pontuacaoRegistrada.valor = valorVenda
        pontuacaoRegistrada.pontos =  pontosGanho
        pontuacaoRegistrada.referenciaExterna = numeroPedido;
        pontuacaoRegistrada.dataVencimento =  plano.obtenhaDataVencimento();

        cartao.creditePontos(pontuacaoRegistrada.pontos)

        if(!contato.ultimaVisita || horarioVenda.getTime() > new Date(contato.ultimaVisita).getTime()){
          contato.ultimaVisita =  horarioVenda;
          await  new MapeadorDeContato().atualizeUltimaVisita(contato);
        }

        let acaoDoContato: AcaoDoContato = AcaoDoContato.ganhouPontos(cartao.contato, pontuacaoRegistrada);

        this.insiraPontuacaoCartao(cartao, pontuacaoRegistrada, acaoDoContato, false,
          true).then( (resposta) => {
          commit( () => {
            resolve(pontuacaoRegistrada);
          })
        }).catch( (erro) => {
          console.log(erro)
          conexao.rollback( () => {
            reject(erro);
          })
        });
      });
    })
  }

  async  salvePontuacaoAutomatica(pedido: PedidoGenerico, empresa: Empresa){
    return new Promise<void>( async (resolve, reject) => {
      if(!empresa.integracaoPedidoFidelidade)
        return  resolve();

      let mapeador = new MapeadorDePontuacaoRegistrada();

      let jaPontou = await (pedido.ehComanda() ? mapeador.selecioneJaPontouComanda(pedido) :
                                                 mapeador.selecioneJaPontou(pedido) ) ;

      if(jaPontou) return  resolve();

      let pontuacaoRegistrada = new PontuacaoRegistrada();
      let plano = empresa.integracaoPedidoFidelidade.plano;
      let atividade = empresa.integracaoPedidoFidelidade.atividade;

      pontuacaoRegistrada.valor = pedido.valor
      pontuacaoRegistrada.pontos = pedido.pontosGanhos;

      if(pedido.ehComanda()){
        pontuacaoRegistrada.comanda = pedido;
      } else {
        pontuacaoRegistrada.pedido = pedido;
      }

      pontuacaoRegistrada.atividades = [ atividade];

      pontuacaoRegistrada.codigo = uuidv1();
      pontuacaoRegistrada.referenciaExterna = pedido.id.toString();
      pontuacaoRegistrada.dataVencimento =  plano.obtenhaDataVencimento();


      let cartao =
        await new MapeadorDeCartao().selecioneSync({idContato: pedido.contato.id, idPlano: plano.id });

      if(!cartao){
        cartao = new Cartao(null, pedido.contato, plano, 0);
        await new MapeadorDeCartao().insiraSync(cartao)
      }

      pontuacaoRegistrada.cartao = cartao;

      cartao.creditePontos(pontuacaoRegistrada.pontos)

      if(!pedido.contato.ultimaVisita || new Date(pedido.horario).getTime() > new Date(pedido.contato.ultimaVisita).getTime()){
        pedido.contato.ultimaVisita =  pedido.horario;
        await  new MapeadorDeContato().atualizeUltimaVisita(pedido.contato);
      }

      if(cartao.plano.renovarAoPontuar && pontuacaoRegistrada.dataVencimento)
        await this.atualizeVencimentoDosPontos(cartao, pontuacaoRegistrada.dataVencimento);


      let acaoDoContato: AcaoDoContato = AcaoDoContato.ganhouPontos(cartao.contato, pontuacaoRegistrada);

      if(moment().diff(pedido.horarioAtualizacao, 'd') > 1){ //executou fora do dia
        pontuacaoRegistrada.horario = pedido.horarioAtualizacao
        acaoDoContato.horario = pedido.horarioAtualizacao
      }

      this.insiraPontuacaoCartao(cartao, pontuacaoRegistrada, acaoDoContato, false,
        true).then( (resposta) => {
         resolve();
      }).catch( (erro) => {
         console.log(erro)
         resolve(erro);
      });
    })

  }

  salvePontuacao(operador: any, pontuacaoRegistrada: PontuacaoRegistrada, pontosCalculados: number, cashback: number): Promise<string> {
    return new Promise<string>( (resolve, reject) => {
      const mapeadorDeContato = new MapeadorDeContato(),
        cartao = pontuacaoRegistrada.cartao,
        atividades = pontuacaoRegistrada.atividades;

      const contato = cartao.contato;

      let tipoDePontuacao: TipoDePontuacao;
      let cartaoQueGanhaOsPontos: Cartao = null,
        contatoQueGanhaOsPontos: Contato = null;
      let objPontuacaoQuemIndica: any = null;

      let codigoResgate: string, valorResgate: number;

      mapeadorDeContato.transacao(async (conexao: any, commit: Function) => {
        async.series([
          (cb: Function) => {
            if (!cartao)
              return cb('Nenhum cartão informado');

            if(!cartao.plano.ativo)
              return cb('Plano foi desativado')

            if (!atividades.length)
              return cb('Nenhuma atividade informada');

            if (!pontuacaoRegistrada.valor && !cashback)
              return cb('Informe um valor de venda maior que 0');

            let atividadeForaPlano = _.find(atividades, (atividade) => atividade.plano.id !== cartao.plano.id)

            if(atividadeForaPlano)
              return cb('Atividade  \"' + atividadeForaPlano.nome + '\" não faz parte do plano \"' + cartao.plano.nome + "\"");

            if(cartao.plano.temInformarPontos() && !pontuacaoRegistrada.pontosInformado)
                return cb('Informe a pontuação ganha')

            cb();
          },
          (cb: Function)  => {
              if(!cashback) return cb();

              this.executeOperacoesResgateCashback(operador, cartao, cashback).then( (brindeResgatado: any) => {
                codigoResgate = brindeResgatado.codigo;
                valorResgate = brindeResgatado.valorEmPontos;
                cb();
              }).catch( erro => {
                cb(erro)
              })
          },
          (cb: Function) => {
            tipoDePontuacao = cartao.plano.tipoDePontuacao.clone();

            pontuacaoRegistrada.pontos = tipoDePontuacao.calculePontos(pontuacaoRegistrada.empresa, pontuacaoRegistrada, null);


            if(Number(pontosCalculados) > 0 && pontuacaoRegistrada.pontos !== pontosCalculados){
              console.log('pontos tela: ' + pontosCalculados + ", pontos calculados: " + pontuacaoRegistrada.pontos)
              return cb('Pontuação inválida: ' + pontuacaoRegistrada.pontos)
            }

            delete  pontuacaoRegistrada.pontosInformado;
            cartao.creditePontos(pontuacaoRegistrada.pontos)
            contato.ultimaVisita = new Date();

            cb();
          },
          (cb: Function) => {
            mapeadorDeContato.atualizeUltimaVisita(contato).then(() => {
              cb();
            });
          },
          (cb: Function) => {
            if( contato.ativarContato()){
              contato.status = contato.estaComoImportado() ? EnumStatusContato.Novo :  EnumStatusContato.Ativo;
              mapeadorDeContato.atualizeStatus(contato).then(() => {
                cb();
              });
            }  else {
              cb();
            }
          },
          (cb: Function) => {

            let acaoDoContato: AcaoDoContato = AcaoDoContato.ganhouPontos(cartao.contato, pontuacaoRegistrada);

            this.insiraPontuacaoCartao(cartao, pontuacaoRegistrada, acaoDoContato).then( (resposta) => {
              cb();
            }).catch( (erro) => {
              cb(erro);
            });
          },
          (cb: Function) => {
            if(!cartao.plano.renovarAoPontuar) return cb();

            let vencimentoPontos = pontuacaoRegistrada.dataVencimento


            this.atualizeVencimentoDosPontos(cartao, pontuacaoRegistrada.dataVencimento).then(() => {
              cb();
            });

          }
          ,
          async () => {
            if( cartao.contato.quemIndicou &&  cartao.contato.quemIndicou.id &&  pontuacaoRegistrada.pontos ) {
              cartaoQueGanhaOsPontos = await new MapeadorDeCartao().selecioneSync({idContato: cartao.contato.quemIndicou.id });
              contatoQueGanhaOsPontos = cartaoQueGanhaOsPontos.contato;
              pontuacaoRegistrada.cartao = cartaoQueGanhaOsPontos;
            }
          },
          (cb: Function) => {
            if( !cartaoQueGanhaOsPontos ) {
              return cb();
            }

            tipoDePontuacao = cartaoQueGanhaOsPontos.plano.tipoDePontuacao.clone();

            objPontuacaoQuemIndica = Object.assign(new PontuacaoRegistrada(), pontuacaoRegistrada);

            delete objPontuacaoQuemIndica.id;

            objPontuacaoQuemIndica.cartao = cartaoQueGanhaOsPontos;

            objPontuacaoQuemIndica.pontos = tipoDePontuacao.calculePontos(pontuacaoRegistrada.empresa, pontuacaoRegistrada, null);

            cartaoQueGanhaOsPontos.pontos += Number(pontuacaoRegistrada.pontos);
            contato.ultimaVisita = new Date();

            cb();
          },
          (cb: Function) => {
            if( !cartaoQueGanhaOsPontos )
              return cb();

            const acaoDoContato: AcaoDoContato = AcaoDoContato.ganhouPontosIndicacao(contato, contatoQueGanhaOsPontos,
              objPontuacaoQuemIndica);

            this.insiraPontuacaoCartao(cartaoQueGanhaOsPontos, objPontuacaoQuemIndica, acaoDoContato).then( (resposta: any) => {
              cb();
            }).catch( (erro: any) => {
              cb(erro);
            });
          }
        ], (erro: any) => {
          if ( !erro ) {
            commit( () => {
              resolve(codigoResgate);
            });
          } else {
            conexao.rollback(() => {
              reject(erro);
            });
          }
        });
      });
    });
  }

  private async atualizeVencimentoDosPontos(cartao: Cartao, novoVencimento: Date) {
    return new Promise((resolve, reject) => {
      (new MapeadorDePontuacaoRegistrada()).renoveVencimento(cartao, novoVencimento).then(() => {
        resolve(null)
      })
    })
  }

  private async insiraPontuacaoCartao(cartao: Cartao, pontuacaoRegistrada: PontuacaoRegistrada,
                                      acaoDoContato: AcaoDoContato, notificarPontuacao: boolean = true,
                                      notificarCartaoCompleto: boolean = true) {

    const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(pontuacaoRegistrada.cartao.empresa));
    let contato = cartao.contato;

    return new Promise<void>( (resolve, reject) => {

      async.series([
        (cb: Function) => {
          new MapeadorDeCartao().atualizeSync(cartao).then(() => {
            cb();
          });
        },
        (cb: Function) => {
          const mapeadorDePontuacao = new MapeadorDePontuacaoRegistrada();

          mapeadorDePontuacao.insiraSync(pontuacaoRegistrada).then( () => {
            mapeadorDePontuacao.insiraAtividades( pontuacaoRegistrada).then( () => {
              cb();
            })
          });
        },
        (cb: Function) => {
          const mapeadorDeAcaoDoContato: MapeadorDeAcaoDoContato = new MapeadorDeAcaoDoContato();

          mapeadorDeAcaoDoContato.insiraSync(acaoDoContato).then( () => {
            cb();
          });
        },
        (cb: Function) => {
          if(!notificarCartaoCompleto && !notificarPontuacao) return cb();

          if(pontuacaoRegistrada.pontos === 0 ){
            console.log('Nenhum pontuaçao gerada, não notificar')
            return  cb();
          }
          const mapeadorDeBrinde = new MapeadorDeBrinde();
          mapeadorDeBrinde.listeAsync({pontos: cartao.pontos}).then( (brindes) => {
            if ( brindes.length > 0 ) {
              if(notificarCartaoCompleto)
                notificacaoService.envieNotificacaoCartaoCompleto(contato).then( (resposta: Resposta<any>) => {
                  if(resposta.erro) console.warn(resposta.erro)
                  cb();
                });
              else return cb();

            } else {
              if(!notificarPontuacao) return cb();

              notificacaoService.envieNotificacaoGanhouPontos(contato).then( (resposta: Resposta<any>) => {
                if(resposta.erro) console.warn(resposta.erro)
                cb();
              });
            }
          });
        }], (erro: any) => {
        if( erro )
          return reject(erro);

        resolve();
      });
    });
  }


  executeOperacoesTroca(brindeResgatado: BrindeResgatado): Promise<any>{
    return new Promise<void>( async (resolve, reject) => {
      let cartao = brindeResgatado.cartao;
      let contato = cartao.contato;
      let pontosResgatar =  brindeResgatado.valorEmPontos;

      if(!cartao.plano.ehCashback() ){
        if (cartao.pontos < pontosResgatar)
          return reject('Brinde  ' + brindeResgatado.obtenhaNomeBrinde() + ' não pode ser resgatado, saldo insuficiente');
      }

      cartao.debitePontos(pontosResgatar, brindeResgatado)

      await new MapeadorDeCartao().atualizeSync(cartao) ;
      await  new MapeadorDeBrindeResgatado().insiraSync(brindeResgatado);
      await this.consumaPontosDaTroca(cartao, pontosResgatar)

      const acaoDoContato: AcaoDoContato = AcaoDoContato.trocouPontos(contato, brindeResgatado);

      await new MapeadorDeAcaoDoContato().insiraSync(acaoDoContato)

      resolve( )

    })
  }


  troquePontos(operador: any, cartao: Cartao, brinde: Brinde, mensagem: string, saldoUtilizar: number = null) {

    return new Promise<BrindeResgatado>( (resolve, reject) => {

      const mapeadorDeContato = new MapeadorDeContato();

      mapeadorDeContato.transacao(async   (conexao: any, commit: Function) => {
        let pontosResgatar = saldoUtilizar > 0 ? saldoUtilizar : brinde.valorEmPontos;

        let brindeResgatado: BrindeResgatado = new BrindeResgatado(operador, cartao, brinde, pontosResgatar, mensagem);

        try{
            await this.executeOperacoesTroca(brindeResgatado)

          let contato = cartao.contato;

          contato.ultimaVisita = new Date();

          await  mapeadorDeContato.atualizeSync(contato);

          const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(contato.empresa));

          notificacaoService.envieNotificacaoResgatouCartao(contato, brindeResgatado.mensagem ).then(() => {
            commit(() => {
              resolve(brindeResgatado);
            });
          });

        }catch (erro) {
          conexao.rollback(() => {
            reject(erro);
          });
        }

      });
    });
  }

  estornePontosConsumidos(cartao: Cartao, valorTrocar: number){
    return new Promise<void>( async (resolve, reject) => {
      if (!cartao.plano.pontosExpiraram()) resolve();
      console.log('estornar consumo dos pontos do cartão ' + cartao.contato.nome + ": " + valorTrocar);

      let pontuacoesConsumidas: any = await new MapeadorDePontuacaoRegistrada().selecioneUltimasUsadas(cartao);
      let pontuacoesAtualizadas: any =  [];
      pontuacoesConsumidas.forEach(  (pontuacaoRegistrada: PontuacaoRegistrada) => {
        if(valorTrocar > 0){
          if( pontuacaoRegistrada.pontosUsados > valorTrocar){
            pontuacaoRegistrada.pontosUsados -= valorTrocar;
            valorTrocar = 0;
          } else {
            valorTrocar -= pontuacaoRegistrada.pontosUsados;
            pontuacaoRegistrada.pontosUsados = 0;

          }

          pontuacoesAtualizadas.push(pontuacaoRegistrada)
        }
      })

      for(let pontuacaoRegistrada of pontuacoesAtualizadas){
        await new MapeadorDePontuacaoRegistrada().atualizePontosUsados(pontuacaoRegistrada)
      }

      resolve();
    });
  }

  consumaPontosDaTroca(cartao: Cartao, valorTrocar: number){
    return new Promise<void>( async (resolve, reject) => {
      if(!cartao.plano.pontosExpiraram()) resolve();

      console.log('consumir pontos do cartão ' + cartao.contato.nome + ": " + valorTrocar);

      let pontuacoesDisponiveis: any = await new MapeadorDePontuacaoRegistrada().listePontuacoesDisponveis(cartao);
      let pontuacoesUsadas: any =  [];

      pontuacoesDisponiveis.forEach(  (pontuacaoRegistrada: PontuacaoRegistrada) => {
          let pontosDisponiveis =  pontuacaoRegistrada.obtenhaPontosDisponiveis();

          if(valorTrocar > 0 && pontosDisponiveis > 0){
            if(pontosDisponiveis > valorTrocar){
              pontuacaoRegistrada.pontosUsados += valorTrocar;
              valorTrocar = 0;
            } else {
              pontuacaoRegistrada.pontosUsados += pontosDisponiveis;
              valorTrocar -= pontosDisponiveis;
            }

            pontuacoesUsadas.push(pontuacaoRegistrada)
          }
      })

      for(let pontuacaoRegistrada of pontuacoesUsadas){
        await new MapeadorDePontuacaoRegistrada().atualizePontosUsados(pontuacaoRegistrada)
      }

      resolve();
    })
  }

  private valideResgateCashback(brinde: Brinde, cartao: Cartao, saldoUtilizar: number, valorPedido: number){
    if(!brinde)
      return 'Nenhum brinde cadastrado para o resgate';

    if(!cartao)
      return 'É necessário informar um cartão para resgatar o saldo de cashback';

    if(!saldoUtilizar) return 'Saldo a ser utilizado no resgate não informado.'

    if (cartao.pontos < saldoUtilizar)
      return 'Saldo de cashback é insuficiente '

    if(cartao.pontos < brinde.valorEmPontos)
      return 'Valor mínimo para resgate é de R$' + brinde.valorEmPontos;

    if(cartao.plano.valorMinimoPontuar && valorPedido < cartao.plano.valorMinimoResgate)
      return 'Para usar seu saldo, o valor mínimo do pedido deve ser de ' +
        FormatadorUtils.numeroParaCurrency(cartao.plano.valorMinimoResgate);
  }

  async executeOperacoesResgateCashback(operador: any, cartao: Cartao, cashback: number, pedido: any = null) {
    let brinde: Brinde =  await new MapeadorDeBrinde().selecioneSync({ idPlano: cartao.plano.id });
    let notificacao = await new MapeadorDeNotificacao().selecioneSync({ tipoDeNotificacao: TipoDeNotificacaoEnum.ResgatouCartao });
    let mensagemResgate = (await   notificacao.obtenhaMensagemProcessada(cartao.empresa, cartao.contato)).mensagemFinal;

    return new Promise<BrindeResgatado>(  async (resolve, reject) => {
      let erro = this.valideResgateCashback(brinde, cartao, cashback, pedido ? pedido.valor : null)

      if(erro) return reject(erro);

      let brindeResgatado = new BrindeResgatado(operador, cartao, brinde, cashback, mensagemResgate, pedido);

      cartao.debitePontos(cashback, brindeResgatado);

      await new MapeadorDeBrindeResgatado().insiraSync(brindeResgatado);

      const acaoDoContato: AcaoDoContato = AcaoDoContato.trocouPontos(cartao.contato, brindeResgatado);

      await new MapeadorDeAcaoDoContato().insiraSync(acaoDoContato);

      await this.consumaPontosDaTroca(cartao, brindeResgatado.valorEmPontos);

      if(!pedido){ // se esta vinculado a um pedido, nao notificar
        const notificacaoService = new NotificacaoService(Ambiente.Instance.novoEnviadorDeMensagens(cartao.empresa));

        await notificacaoService.envieNotificacaoResgatouCartao(cartao.contato, brindeResgatado.mensagem );
      }

      resolve(brindeResgatado)
    });
  }

  async registrePontuacaoVencida(cartao: Cartao, pontuacoesRegistradas: Array<PontuacaoRegistrada>) {
    const mapeadorPontuacaoRegistrada = new MapeadorDePontuacaoRegistrada();

    return new Promise<void>( async (resolve, reject) => {

      mapeadorPontuacaoRegistrada.transacao(async (conexao: any, commit: Function) => {
          for (let i = 0; i < pontuacoesRegistradas.length; i++){
            //add if pra casos que vencimento e setado direto banco e cartão ja zerou pts antes de computar todos vencimentos
            if(cartao.pontos > 0){
              let pontuacaoRegistrada = pontuacoesRegistradas[i];

              const acaoDoContato: AcaoDoContato = AcaoDoContato.venceuPontos(pontuacaoRegistrada);
              let pontosVencidos = pontuacaoRegistrada.obtenhaQtdePontosVenceu();

              pontuacaoRegistrada.pontosVencidos = pontosVencidos;
              cartao.debitePontos(pontosVencidos);

              await new MapeadorDeCartao().atualizeSync( cartao);
              await mapeadorPontuacaoRegistrada.atualizePontosVencidos(pontuacaoRegistrada)
              await new MapeadorDeAcaoDoContato().insiraSync(acaoDoContato);

              console.log(
                String(`${pontosVencidos} ${pontuacaoRegistrada.otenhaTipoDeAcumulo()} expiraram do ${acaoDoContato.contato.nome}`));
            }
          }

          commit( () => {
            resolve();
          })
        })
    })
  }


  async executeOperacoesEstornoBrindeResgatado(brindeResgatado: any) {
    if(brindeResgatado.removido) return;

    let acaoContato = await new MapeadorDeAcaoDoContato().selecioneSync({ idBrindeResgatado: brindeResgatado.id});

    brindeResgatado.cartao.creditePontos(brindeResgatado.valorEmPontos);
    await this.estornePontosConsumidos(brindeResgatado.cartao, brindeResgatado.valorEmPontos)
    await new MapeadorDeBrindeResgatado().removaAsync(brindeResgatado);
    await new MapeadorDeAcaoDoContato().removaAsync(acaoContato);
    await new MapeadorDeCartao().atualizeSync(brindeResgatado.cartao);


  }


  estornePontosVendaExterna(pontuacaoRegistrada: any){
    return new Promise<void>( async (resolve, reject) => {
      new MapeadorDePedido().transacao( async (conexao: any, commit: any) => {
        await this.executeOperacoesEstornoPontos(pontuacaoRegistrada);

        commit( () => { resolve(null)})
      })
    });
  }

  estornePontosPorOperador(pontuacaoRegistrada: any, operador: any, motivo: string){
    return new Promise<string>( async (resolve, reject) => {
      new MapeadorDePedido().transacao( async (conexao: any, commit: any) => {

        if(pontuacaoRegistrada.pontosUsados > 0) return resolve('Pontos já foram usados.')
        if(pontuacaoRegistrada.pontosVencidos > 0) return resolve('Pontos já expiraram.')

        await this.executeOperacoesEstornoPontos(pontuacaoRegistrada);
        await new PontuacaoEstornada(pontuacaoRegistrada, operador, motivo).salve(true);

        commit( () => { resolve(null)})
      })
    });
  }


  async executeOperacoesEstornoPontos(pontuacaoRegistrada: PontuacaoRegistrada){
    let cartao = pontuacaoRegistrada.cartao;

    cartao.debitePontos(pontuacaoRegistrada.pontos) ;

    let acaoContato = await new MapeadorDeAcaoDoContato().selecioneSync({ idPontuacao: pontuacaoRegistrada.id});

    pontuacaoRegistrada.removida = true;

    await new MapeadorDeCartao().atualizeSync(cartao);
    await new MapeadorDePontuacaoRegistrada().removaAsync(pontuacaoRegistrada);
    await new MapeadorDeAcaoDoContato().removaAsync(acaoContato);

  }
}
