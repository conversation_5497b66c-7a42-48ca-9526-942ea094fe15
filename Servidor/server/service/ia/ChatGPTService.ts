import {Configuration, OpenAIApi} from "openai";
import {Ambiente} from "../Ambiente";
import {ChamadaIa} from "../../domain/chatbot/ChamadaIa";
import {MapeadorDeChamadaIa} from "../../mapeadores/MapeadorDeChamadaIa";
const axios = require("axios");

export class ChatGPTService {
  private apiKey: string =  '********************************************************';

  private configuration = new Configuration({
    //apiKey: '***************************************************',
    apiKey: '********************************************************'
  });

  async chameChatgpt2(prompt: string) {
    return new Promise(async (resolve, reject) => {
      const data = { msg: prompt };

      const response = await axios.post('http://localhost:4000/mia/index', data);

      //onFinish(response.data);
      resolve(response.data);
    });
  }

  async chameChatgptChatAsync(prompt: string, mensagens: Array<any>, onProgress: Function, onFinish: Function) {
    const openai = new OpenAIApi(this.configuration);

    console.log(prompt);

    const listaDeMensagens = [{role: "system", content: prompt}];
    //cria uma lista que une listaDeMensagens com mensagens
    const listaCompleta: any = listaDeMensagens.concat(mensagens);

    //console.log(listaCompleta);

    const resposta: any = await openai.createChatCompletion({
      model: "gpt-3.5-turbo",
      messages: listaCompleta,
      temperature: 0.7,
      presence_penalty: 0.0,
      top_p: 1,
      max_tokens: 250,
      stream: true
    }, { responseType: 'stream' });

    console.log('[mia] ' + listaCompleta.map((conversa: any) => {
      return `--${conversa.role}: ${conversa.content}`}).join("\n"));
    const result = {
      role: 'assistant',
      text: '',
      usage: {}
    }

    resposta.data.on('data', (data: Buffer) => {
      const mensagem = '';
      const lines = data.toString().split('\n').filter( (line: string) => line.trim() !== '');
      for (const line of lines) {
        const message = line.replace(/^data: /, '');
        if (message === '[DONE]') {
          onFinish(result);
          console.log('terminou');
          return; // Stream finished
        }
        try {
          const parsed = JSON.parse(message);
          if( parsed.usage ) {
            result.usage = parsed.usage;
          }
          if( parsed.choices[0].delta.content ) {
            result.text += parsed.choices[0].delta.content;
          }
          onProgress(result);
        } catch(error) {
          console.error('Could not JSON parse stream message', message, error);
        }
      }
    });
  }

  async chameChatgpt(prompt: string, onProgress: Function, onFinish: Function) {
    const openai = new OpenAIApi(this.configuration);

    console.log(prompt);

    const resposta: any = await openai.createCompletion({
      model: "text-davinci-003",
      prompt: prompt,
      temperature: 0.7,
      presence_penalty: 0.0,
      best_of: 1,
      top_p: 1,
      max_tokens: 250,
      stream: true
    }, { responseType: 'stream' });

    console.log(prompt);
    const result = {
      role: 'assistant',
      text: '',
      usage: {}
    }

    resposta.data.on('data', (data: any) => {
      const lines = data.toString().split('\n').filter( (line: string) => line.trim() !== '');
      for (const line of lines) {
        const message = line.replace(/^data: /, '');
        console.log('mensagem: ', message);

        if (message === '[DONE]') {
          onFinish(result);
          console.log('terminou');
          return; // Stream finished
        }
        try {
          const parsed = JSON.parse(message);
          if( parsed.usage ) {
            result.usage = parsed.usage;
          }
          result.text += parsed.choices[0].text;
          onProgress(result);
        } catch(error) {
          console.error('Could not JSON parse stream message', message, error);
        }
      }
    });
  }

  async chameOpenAI(prompt: string, temperatura: number) {
    return new Promise( async (resolve, reject) => {
      const openai = new OpenAIApi(this.configuration);

      console.log(prompt);

      const resposta: any = await openai.createCompletion({
        model: "gpt-4o-mini",
        prompt: prompt,
        temperature: temperatura,
        presence_penalty: 0.0,
        best_of: 1,
        top_p: 1,
        max_tokens: 1500
      }, {
        responseType: 'stream',
      });

      const result = {
        role: 'assistant',
        text: '',
        usage: resposta.data.usage
      };
      console.log(result);
      result.text += resposta.data.choices[0].text;
      resolve(result);
      return;
    });
  }

  async chameOpenAIChat(telefone: string, intent: string, prompt: string, mensagem: string, mensagens: Array<any>, temperatura: number,
                        tagLog: string = '[chatbot]', formatoResposta: any, functions?: any[], functionCall?: any,
                        modelo: string = 'gpt-4o-mini', linguagem: string = 'pt-BR'): Promise<any> {
    return new Promise( async (resolve, reject) => {
      // Imprimir prompt formatado para debug
      console.log('\n' + '='.repeat(80));
      console.log(`${tagLog} PROMPT FORMATADO - Telefone: ${telefone} - Intent: ${intent}`);
      console.log('='.repeat(80));
      console.log('SYSTEM PROMPT:');
      console.log('-'.repeat(80));
      console.log(prompt);
      console.log('-'.repeat(80));

      if (mensagens && mensagens.length > 0) {
        console.log('\nMENSAGENS DO CONTEXTO:');
        console.log('-'.repeat(80));
        mensagens.forEach((msg, index) => {
          console.log(`[${index + 1}] ${msg.role.toUpperCase()}: ${msg.content}`);
        });
        console.log('-'.repeat(80));
      } else {
        console.log('\nNENHUMA MENSAGEM NO CONTEXTO');
      }

      console.log('='.repeat(80) + '\n');

      const listaDeMensagens = [{role: "system", content: prompt}];
      //cria uma lista que une listaDeMensagens com mensagens
      const listaCompleta: any = listaDeMensagens.concat(mensagens);

      let resposta: any = null;
      let inicio = new Date();

      const chamadaIa = new ChamadaIa();

      const result = {
        role: 'assistant',
        text: '',
        sucesso: true,
        usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 },
        estrutura: null as any,
        function_call: null as any
      };

      const modelo = 'gpt-4o-mini';

      try {
        const url = 'https://api.openai.com/v1/chat/completions';

        const requestBody: any = {
          messages: listaCompleta,
          model: modelo,
          max_tokens: 1500,
          temperature: temperatura
        };

        // Adicionar response_format se fornecido
        if (formatoResposta) {
          requestBody.response_format = formatoResposta;
        }

        // Adicionar functions e function_call se fornecidos
        if (functions && functions.length > 0) {
          requestBody.functions = functions;
          requestBody.function_call = functionCall || "auto";
          console.log(`${tagLog} Function calling habilitado com ${functions.length} funções`);
        }

        resposta = await axios.post(url, requestBody, {
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ********************************************************',
          },
        });

        console.log(resposta.data);
      } catch (error) {
        console.log(error);
        if( error.response ) {
          console.log(error.response.data.error);
        }
        console.log(error.message);
        // ... tratamento de erro existente ...
      }

      if (resposta) {
        const messageChoice = resposta.data.choices[0].message;
        const functionCall = messageChoice.function_call;

        if (functionCall) {
          // Resposta com function call
          console.log(`${tagLog} Function call detectada: ${functionCall.name}`);
          console.log(`${tagLog} Argumentos: ${functionCall.arguments}`);

          // Retornar dados da função para processamento posterior
          result.function_call = {
            name: functionCall.name,
            arguments: functionCall.arguments
          };

          // Para compatibilidade, manter text vazio se há function call
          result.text = '';

          // Caso especial para função antiga
          if (functionCall.name === "gerar_resposta_estruturada") {
            const parsedArguments = JSON.parse(functionCall.arguments);
            result.estrutura = parsedArguments.estrutura;
            result.text = parsedArguments.texto;
          }
        } else {
          // Resposta normal de texto
          result.text = messageChoice.content || '';
        }

        result.usage = resposta.data.usage;

        chamadaIa.promptTokens = result.usage.prompt_tokens;
        chamadaIa.completionTokens = result.usage.completion_tokens;

        chamadaIa.totalTokens = result.usage.total_tokens;
      }

      chamadaIa.resposta = result.text;

      // Log da resposta formatada
      console.log('\n' + '='.repeat(80));
      console.log(`${tagLog} RESPOSTA DO ChatGPT`);
      console.log('='.repeat(80));
      console.log(result.text);
      console.log('-'.repeat(80));
      console.log(`Tempo de resposta: ${(new Date().getTime() - inicio.getTime()) / 1000}s`);
      if (result.usage) {
        console.log(`Tokens usados: ${result.usage.total_tokens} (prompt: ${result.usage.prompt_tokens}, completion: ${result.usage.completion_tokens})`);
      }
      console.log('='.repeat(80) + '\n');

      chamadaIa.mensagem = mensagem;
      chamadaIa.prompt = listaCompleta.map((conversa: any) => {
        return `--${conversa.role}: ${conversa.content}`}).join("\n");
      chamadaIa.api = 'openai-' + modelo;
      chamadaIa.dataCriacao = new Date();
      chamadaIa.horarioCriacao = new Date();

      chamadaIa.tempoChamada = (new Date().getTime() - inicio.getTime()) / 1000;
      chamadaIa.intent = intent;

      await new MapeadorDeChamadaIa().insiraGraph(chamadaIa);

      resolve(result);
      return;
    });
  }

  /**
   * Chama OpenAI Chat com suporte completo a function calling em loop
   * Executa funções até obter uma resposta final sem function calls
   */
  async chameOpenAIChatWithFunctionLoop(
    telefone: string,
    intent: string,
    prompt: string,
    mensagem: string,
    mensagensIniciais: Array<any>,
    temperatura: number,
    tagLog: string = '[chatbot]',
    formatoResposta: any,
    functions?: any[],
    functionCall?: any,
    executorFuncoes?: (nome: string, args: any) => Promise<any>,
    maxIteracoes: number = 10
  ): Promise<any> {
    console.log(`${tagLog} Iniciando chat com function loop - max iterações: ${maxIteracoes}`);

    // Construir mensagens iniciais
    const mensagens = [
      { role: "system", content: prompt },
      ...mensagensIniciais
    ];

    let iteracao = 0;
    let respostaFinal: any = null;

    // Loop até obter resposta sem function call ou atingir limite
    while (iteracao < maxIteracoes) {
      iteracao++;
      console.log(`${tagLog} Iteração ${iteracao}/${maxIteracoes}`);

      // Fazer chamada à API
      const resposta = await this.chameOpenAIChat(
        telefone,
        intent,
        prompt,
        mensagem,
        mensagens.slice(1), // Remove system message que já está no prompt
        temperatura,
        `${tagLog}[iter-${iteracao}]`,
        formatoResposta,
        functions,
        functionCall
      );

      // Se não há function call, temos a resposta final
      if (!resposta.function_call) {
        console.log(`${tagLog} Resposta final obtida na iteração ${iteracao}`);
        respostaFinal = resposta;
        break;
      }

      // Há function call - executar função
      console.log(`${tagLog} Function call detectada: ${resposta.function_call.name}`);

      if (!executorFuncoes) {
        console.error(`${tagLog} Executor de funções não fornecido!`);
        respostaFinal = resposta;
        break;
      }

      try {
        // Parsear argumentos e executar função
        const args = JSON.parse(resposta.function_call.arguments);
        console.log(`${tagLog} Executando função ${resposta.function_call.name} com args:`, args);

        const resultadoFuncao = await executorFuncoes(resposta.function_call.name, args);
        console.log(`${tagLog} Resultado da função:`, JSON.stringify(resultadoFuncao).substring(0, 200) + '...');

        // Adicionar mensagem do assistant com function call
        mensagens.push({
          role: "assistant",
          content: null,
          function_call: resposta.function_call
        });

        // Adicionar resultado da função
        mensagens.push({
          role: "function",
          name: resposta.function_call.name,
          content: JSON.stringify(resultadoFuncao)
        });

      } catch (erro: any) {
        console.error(`${tagLog} Erro ao executar função:`, erro);

        // Adicionar mensagem de erro
        mensagens.push({
          role: "assistant",
          content: null,
          function_call: resposta.function_call
        });

        mensagens.push({
          role: "function",
          name: resposta.function_call.name,
          content: JSON.stringify({ erro: erro.message })
        });
      }
    }

    if (!respostaFinal) {
      console.warn(`${tagLog} Limite de iterações atingido sem resposta final`);
      // Forçar uma resposta final
      const respostaForcada = await this.chameOpenAIChat(
        telefone,
        intent,
        prompt,
        "Por favor, forneça uma resposta final baseada nas informações coletadas.",
        mensagens.slice(1),
        temperatura,
        `${tagLog}[final]`,
        formatoResposta,
        [], // Sem functions para forçar resposta
        "none"
      );
      respostaFinal = respostaForcada;
    }

    console.log(`${tagLog} Function loop concluído após ${iteracao} iterações`);
    return respostaFinal;
  }
}
