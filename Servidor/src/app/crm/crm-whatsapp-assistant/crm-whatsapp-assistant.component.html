<!-- WhatsApp Assistant - Layout Chat SDR Premium -->
<div class="whatsapp-assistant">

  <!-- Loading -->
  <div *ngIf="carregandoDados" class="loading-screen">
    <div class="spinner-border" role="status">
      <span class="sr-only">Carregando...</span>
    </div>
    <p>Carregando dados do contato...</p>
  </div>

  <!-- Layout Principal -->
  <div *ngIf="!carregandoDados" class="chat-layout">

    <!-- Header do Chat -->
    <div class="chat-header">
      <div class="assistant-info">
        <div class="assistant-avatar">
          <i class="fe-zap"></i>
        </div>
        <div class="assistant-details">
          <h1 class="assistant-name">Assistente SDR Premium</h1>
          <p class="assistant-subtitle">
            <span class="status-dot"></span>
            Especialista Meu Cardápio • IA Avançada
          </p>
        </div>
      </div>
      <div class="header-actions">
        <button class="header-btn">
          <i class="fe-settings"></i>
        </button>
        <button class="header-btn">
          <i class="fe-message-circle"></i>
        </button>
      </div>
    </div>

    <!-- Card Principal do Lead -->
    <div class="lead-card">
      <div class="lead-content">
        <div class="lead-avatar">
          <div class="avatar-circle">
            {{ getInitials(contato.nomeResponsavel || contato.empresa || nomeWhatsApp) }}
          </div>
        </div>
        <div class="lead-info">
          <div class="lead-name-section">
            <h1 class="lead-name">{{ contato.nomeResponsavel || contato.empresa || nomeWhatsApp || 'NOVO CONTATO' }}</h1>
            <span class="phone-tag" *ngIf="telefone">
              <i class="fe-phone"></i>
              {{ formatarTelefone(telefone) }}
            </span>
          </div>
          <div class="lead-status">
            <span class="status-badge" [class.registered]="contato.id" [class.not-registered]="!contato.id">
              {{ contato.id ? 'Lead Cadastrado' : 'Não Cadastrado no Bitrix' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerta de Lead Não Cadastrado no Bitrix -->
    <div class="alert-not-registered" *ngIf="!contato.id">
      <div class="alert-content">
        <div class="alert-icon">
          <i class="fe-user-x"></i>
        </div>
        <div class="alert-text">
          <h3>📋 Contato não cadastrado</h3>
          <p class="main-message">
            <strong>CNPJ ausente:</strong> CNPJ não informado. Adicione o CNPJ da empresa para validação e credibilidade.
          </p>
          <p class="fix-instruction">
            <em>Corrija esses dados para melhorar significativamente a personalização e efetividade das mensagens de vendas.</em>
          </p>
        </div>
        <div class="alert-action">
          <button class="fix-btn" (click)="abrirCadastroBitrix()">
            <i class="fe-edit-2"></i>
            Corrigir
          </button>
        </div>
      </div>
    </div>

    <!-- Alerta de Lead Mal Cadastrado (para leads já cadastrados) -->
    <div class="alert-bad-lead" *ngIf="contato.id && getProblemasLead().length > 0">
      <div class="alert-content">
        <div class="alert-icon">
          <i class="fe-alert-triangle"></i>
        </div>
        <div class="alert-text">
          <h3>🚨 Lead mal cadastrado!</h3>
          <div class="problems-list">
            <p *ngFor="let problema of getProblemasLead()" class="problem-item">
              <strong>{{ problema.tipo }}:</strong> {{ problema.descricao }}
            </p>
          </div>
          <p class="fix-instruction">
            <strong>Corrija esses dados</strong> para melhorar significativamente a personalização e efetividade das mensagens de vendas.
          </p>
        </div>
        <div class="alert-action">
          <button class="fix-btn" (click)="abrirEdicaoLead()">
            <i class="fe-edit-2"></i>
            Corrigir
          </button>
        </div>
      </div>
    </div>

    <!-- Card Detalhes do Lead -->
    <div class="detail-card">
      <div class="card-header">
        <div class="header-left">
          <i class="fe-user"></i>
          <span>Detalhes</span>
        </div>
        <div class="header-right" *ngIf="contato.idBitrix">
          <a [href]="getBitrixUrl()" target="_blank" class="bitrix-btn">
            <i class="fe-grid"></i>
            Abrir no Bitrix
          </a>
        </div>
      </div>
      <div class="detail-grid" *ngIf="contato && contato.crmEmpresa">
        <div class="detail-item">
          <div class="detail-icon empresa">
            <i class="fe-briefcase"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">EMPRESA</div>
            <div class="detail-value">{{ contato.crmEmpresa?.nome || contato.empresa }}</div>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon telefone">
            <i class="fe-phone"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">TELEFONE</div>
            <div class="detail-value">{{ formatarTelefone(contato.telefone) }}</div>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon segmento">
            <i class="fe-tag"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">SEGMENTO2</div>
            <div class="detail-value">{{ contato.segmento || 'Restaurante' }}</div>
          </div>
        </div>

        <div class="detail-item">
          <div class="detail-icon concorrente">
            <i class="fe-external-link"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">SISTEMA ATUAL</div>
            <div class="detail-value">{{ contato.sistemaConcorrente || 'Não informado' }}</div>
          </div>
        </div>

        <div class="detail-item" *ngIf="contato.instagramHandle">
          <div class="detail-icon instagram">
            <i class="fe-instagram"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">INSTAGRAM</div>
            <div class="detail-value">{{ contato.instagramHandle || '@gamanjapanesefood' }}</div>
          </div>
        </div>

        <div class="detail-item" *ngIf="contato.observacoes">
          <div class="detail-icon bio">
            <i class="fe-file-text"></i>
          </div>
          <div class="detail-content">
            <div class="detail-label">BIO INSTAGRAM</div>
            <div class="detail-value bio-text"
                 [innerHTML]="formatarTextoComQuebras(contato.observacoes)"
                 [title]="contato.observacoes">
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- Área de Chat -->
    <div class="chat-area">


      <!-- Mensagens da Conversa SDR-Agente (integradas naturalmente) -->
      <div *ngFor="let mensagem of historicoConversaAgente; let i = index" class="chat-message-wrapper">

        <!-- Mensagem do SDR (usuário) -->
        <div *ngIf="mensagem.role === 'user'" class="chat-message user-message">
          <div class="message-content user-content">
            <div class="message-header">
              <span class="message-sender">Você (SDR)</span>
              <span class="message-time">{{ mensagem.timestamp | date:'HH:mm' }}</span>
            </div>
            <div class="message-text">
              {{ mensagem.content }}
            </div>
            <div class="message-actions" *ngIf="mensagem.temAspas">
              <button class="action-btn copy" (click)="copiarTexto(mensagem.content)">
                <i class="fe-copy"></i>
                Copiar
              </button>
            </div>
          </div>
          <div class="message-avatar user-avatar">
            <i class="fe-user"></i>
          </div>
        </div>

        <!-- Resposta do Agente -->
        <div *ngIf="mensagem.role === 'assistant'" class="chat-message assistant-message">
          <div class="message-avatar agent-avatar">
            <i class="fe-cpu"></i>
          </div>
          <div class="message-content agent-content">
            <div class="message-header">
              <span class="message-sender">Agente IA</span>
              <span class="message-time">{{ mensagem.timestamp | date:'HH:mm' }}</span>
            </div>
            <div class="message-text">
              {{ mensagem.content }}
            </div>
            <div class="message-actions" *ngIf="mensagem.temAspas">
              <button class="action-btn copy" (click)="copiarTexto(mensagem.content)">
                <i class="fe-copy"></i>
                Copiar
              </button>
            </div>
          </div>
        </div>

      </div>





    </div>

  </div>

  <!-- Área de Input do Chat (Fixa na parte inferior) -->
  <div class="chat-input-area">

    <!-- Botões de Sugestões Rápidas -->
    <div class="quick-suggestions" *ngIf="!carregando">
      <button class="quick-btn" (click)="gerarRapport()" title="Rapport pizzaria">
        <i class="fe-heart"></i>
        <span>Rapport</span>
      </button>

      <button class="quick-btn" (click)="gerarSugestaoEspecifica('spin-situacao')" title="SPIN situação">
        <i class="fe-bar-chart-2"></i>
        <span>SPIN</span>
      </button>

      <button class="quick-btn" (click)="gerarSugestaoEspecifica('objecao-dinheiro')" title="Objeção dinheiro">
        <i class="fe-dollar-sign"></i>
        <span>Objeção</span>
      </button>

      <button class="quick-btn" (click)="gerarSugestaoEspecifica('agendar-demo')" title="Agendar demo">
        <i class="fe-calendar"></i>
        <span>Demo</span>
      </button>

      <button class="quick-btn" (click)="gerarSugestaoEspecifica('follow-up')" title="Follow-up">
        <i class="fe-phone-call"></i>
        <span>Follow-up</span>
      </button>

      <button class="quick-btn" (click)="gerarSugestaoEspecifica('vamos-pensar')" title="Vamos pensar">
        <i class="fe-clock"></i>
        <span>Pensar</span>
      </button>
    </div>

    <div class="input-container">
      <input
        type="text"
        class="chat-input"
        placeholder="Digite sua mensagem..."
        [(ngModel)]="mensagemUsuario"
        (keyup.enter)="enviarMensagem()"
        [disabled]="carregando">
      <button
        class="send-btn"
        (click)="enviarMensagem()"
        [disabled]="carregando || !mensagemUsuario?.trim()">
        <i class="fe-send" *ngIf="!carregando"></i>
        <i class="fe-loader rotating" *ngIf="carregando"></i>
      </button>
    </div>
    <div class="input-hint">
      Pressione Enter para enviar • Shift+Enter para quebrar linha
    </div>
  </div>

</div>

