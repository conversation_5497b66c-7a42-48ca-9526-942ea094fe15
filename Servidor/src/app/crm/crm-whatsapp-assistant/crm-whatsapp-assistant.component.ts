import { Component, OnInit, HostListener } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LeadService } from '../services/lead.service';
import { SugestoesService } from '../services/sugestoes.service';
import { TelefonePipe } from '../../telefone.pipe';

@Component({
  selector: 'app-crm-whatsapp-assistant',
  templateUrl: './crm-whatsapp-assistant.component.html',
  styleUrls: ['./crm-whatsapp-assistant.component.scss']
})
export class CrmWhatsappAssistantComponent implements OnInit {
  // Dados do contato
  telefone: string = '';
  contato: any = {
    nome: '',
    telefone: '',
    empresa: '',
    email: '',
    instagramHandle: '',
    sistemaConcorrente: '',
    bioInsta: '',
    id: null
  };

  // Flag para indicar se está rodando em iframe
  private isInIframe: boolean = false;

  // Estado da conversa
  faseSpin: 'rapport' | 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto' = 'situacao';
  contextoResumo: string = '';
  faseDetectadaAutomaticamente: string | null = null;

  // Mensagens capturadas do WhatsApp
  mensagensCapturadas: any[] = [];
  capturandoMensagens: boolean = false;


  carregando: boolean = false;
  erro: string | null = null;

  // Configurações
  tipoTom: 'formal' | 'informal' | 'tecnico' = 'formal';
  produto: string = 'Meu Cardápio';

  // Tipo de abordagem para rapport
  tipoAbordagem: 'direta' | 'indireta' | 'consultiva' = 'consultiva';

  // Modo apresentação
  modoApresentacao: boolean = false;

  // Controle de notificação de cópia
  mensagemCopiada: boolean = false;

  // Estado de carregamento inicial
  carregandoDados: boolean = true;

  // Dados para novo lead
  novoLead: any = {
    empresa: '',
    email: '',
    observacoes: ''
  };

  // Nome do contato vindo do WhatsApp
  nomeWhatsApp: string = '';

  // Fase detectada pela IA
  faseDetectadaPelaIA: string | null = null;
  confiancaDeteccao: number = 0;
  dataUltimaDeteccao: Date | null = null;
  detectandoFase: boolean = false;

  // Propriedades para o chat
  mensagemUsuario: string = '';

  // Histórico da conversa SDR-Agente
  historicoConversaAgente: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }> = [];

  constructor(
    private route: ActivatedRoute,
    private leadService: LeadService,
    private sugestoesService: SugestoesService,
    private telefonePipe: TelefonePipe
  ) { }

  ngOnInit(): void {
    // Detectar se está em iframe
    this.isInIframe = this.inIframe();
    console.log('[WhatsApp Assistant] Rodando em iframe:', this.isInIframe);

    // Começar com loading ativo
    this.carregandoDados = true;

    this.route.params.subscribe(params => {
      // Garantir que loading sempre começa como true
      this.carregandoDados = true;

      // Resetar TODOS os estados para valores neutros
      this.contato = {
        nome: '',
        telefone: '',
        empresa: '',
        email: '',
        instagramHandle: '',
        sistemaConcorrente: '',
        bioInsta: '',
        id: null
      };
      this.mensagensCapturadas = [];
      this.contextoResumo = 'Aguardando captura de mensagens...';
      this.erro = null;

      this.carregando = false;
      this.capturandoMensagens = false;

      // Resetar dados do novo lead
      this.novoLead = {
        empresa: '',
        email: '',
        observacoes: ''
      };

      // Resetar nome do WhatsApp
      this.nomeWhatsApp = '';

      // Limpar histórico da conversa com agente ao trocar de lead
      this.historicoConversaAgente = [];

      // Carregar novo contato
      this.telefone = params['telefone'];
      this.carregarDadosContato();
      this.carregarContextoConversa();

      // Adicionar mensagem inicial do agente
      this.adicionarMensagemInicialAgente();

      // Indicar que está pronto para receber mensagens
      this.capturandoMensagens = true;
    });
  }

  carregarDadosContato(): void {
    // Garantir que loading está ativo
    this.carregandoDados = true;

    if (this.telefone) {
      console.log('[WhatsApp Assistant] Buscando lead por telefone:', this.telefone);

      // Primeiro definir o telefone que já temos
      this.contato.telefone = this.telefone;

      this.leadService.buscarPorTelefone(this.telefone).then(
        (response) => {
          console.log('[WhatsApp Assistant] Resposta da busca no Bitrix24:', response);

          if (response && response.id) {
            // Usar o lead do Bitrix24 - ServerService já processou a resposta
            // Usar o lead completo para ter acesso a todos os dados, incluindo links
            this.contato = response;
            // Garantir que o telefone esteja sempre definido
            this.contato.telefone = this.telefone;


            // Carregar fase detectada pela IA se existir
            if (response.faseSpinDetectada) {
              this.faseDetectadaPelaIA = response.faseSpinDetectada;
              this.confiancaDeteccao = response.confiancaFaseDetectada || 0;
              this.dataUltimaDeteccao = response.dataUltimaDeteccaoFase ? new Date(response.dataUltimaDeteccaoFase) : null;
            }

            console.log('[WhatsApp Assistant] Lead do Bitrix24 encontrado:', this.contato);

          } else {
            // Lead não encontrado no banco - manter como não cadastrado
            this.contato = {
              nomeResponsavel: 'Contato não cadastrado',
              telefone: this.telefone,
              empresa: '',
              email: '',
              instagramHandle: '',
              sistemaConcorrente: '',
              bioInsta: '',
              id: null,
              links: []
            };
            console.log('[WhatsApp Assistant] Lead não encontrado');
          }

          setTimeout(() => {
            this.carregandoDados = false;
          }, 200);
        }
      ).catch(erro => {
        console.error('[WhatsApp Assistant] Erro ao buscar lead:', erro);
        this.contato = {
          nomeResponsavel: 'Contato não cadastrado',
          telefone: this.telefone,
          empresa: '',
          email: '',
          instagramHandle: '',
          sistemaConcorrente: '',
          bioInsta: '',
          id: null,
          links: []
        };

        setTimeout(() => {
          this.carregandoDados = false;
          // Fazer scroll inicial após carregar dados
          setTimeout(() => this.scrollToBottom(), 100);
        }, 200);
      });
    } else {
      this.contato = {
        nomeResponsavel: 'Telefone não informado',
        telefone: '',
        empresa: '',
        email: '',
        instagramHandle: '',
        sistemaConcorrente: '',
        bioInsta: '',
        id: null,
        links: []
      };

      setTimeout(() => {
        this.carregandoDados = false;
        // Fazer scroll inicial após carregar dados
        setTimeout(() => this.scrollToBottom(), 100);
      }, 200);
    }
  }

  carregarContextoConversa(): void {
    // O contexto será atualizado quando receber mensagens do WhatsApp via content script
    this.contextoResumo = 'Aguardando captura de mensagens...';
  }

  // Adicionar mensagem inicial do agente
  private adicionarMensagemInicialAgente(): void {
    // Só adicionar se não houver mensagens no histórico
    if (this.historicoConversaAgente.length === 0) {
      this.historicoConversaAgente.push({
        role: 'assistant',
        content: '👋 Olá! Sou seu assistente de vendas especializado no Meu Cardápio. Como posso ajudar você hoje? Se precisar de alguma orientação sobre como abordar o lead ou informações sobre o Meu Cardápio, estou aqui para ajudar!',
        timestamp: new Date()
      });

      // Fazer scroll para mostrar a mensagem inicial
      setTimeout(() => this.scrollToBottom(), 100);
    }
  }

  async gerarSugestao(): Promise<void> {
    // Método simplificado - agora apenas inicia conversa com agente
    this.enviarMensagem('Preciso de uma sugestão de resposta para este lead.');
  }



  // Método removido - agora sempre usamos IA para gerar sugestões
  // private gerarSugestaoMockada(): string {
  //   const sugestoesPorFase = {
  //     situacao: `Oi ${this.contato.nome}! Obrigado por entrar em contato. Para entender melhor como posso ajudar sua empresa, você poderia me contar como vocês fazem o controle de estoque atualmente nas 3 lojas?`,
  //     problema: `Entendo sua preocupação, ${this.contato.nome}. Essas dificuldades com estoque são bem comuns. Vocês já enfrentaram situações de ruptura de produtos ou excesso de estoque parado?`,
  //     implicacao: `Imagino que isso deve impactar bastante o resultado das lojas, ${this.contato.nome}. Quanto tempo a equipe perde por dia fazendo esse controle manual? E como isso afeta as vendas quando um produto está em falta?`,
  //     necessidade: `Perfeito, ${this.contato.nome}! Se conseguíssemos automatizar esse processo e dar visibilidade em tempo real do estoque das 3 lojas, qual seria o impacto para vocês? Gostaria de ver como o PromoKit pode resolver exatamente essas questões?`
  //   };

  //   return sugestoesPorFase[this.faseSpin];
  // }

  mudarFaseSpin(novaFase: 'rapport' | 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto'): void {
    this.faseSpin = novaFase;
    // Limpar fase detectada se mudar manualmente
    if (novaFase !== 'auto') {
      this.faseDetectadaAutomaticamente = null;
    }
  }





  // Método alternativo para copiar texto
  private copiarTextoFallback(texto: string): void {
    try {
      // Criar elemento textarea temporário
      const textarea = document.createElement('textarea');
      textarea.value = texto;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.left = '-9999px';

      document.body.appendChild(textarea);
      textarea.select();
      textarea.setSelectionRange(0, 99999); // Para mobile

      const sucesso = document.execCommand('copy');
      document.body.removeChild(textarea);

      if (sucesso) {
        console.log('Sugestão copiada com método fallback!');
        this.mostrarFeedback('Sugestão copiada para área de transferência!', 'success');
      } else {
        console.error('Falha ao copiar texto');
        this.mostrarFeedback('Erro ao copiar. Selecione e copie manualmente.', 'error');
      }
    } catch (err) {
      console.error('Erro no método fallback:', err);
      this.mostrarFeedback('Erro ao copiar. Selecione e copie manualmente.', 'error');
    }
  }



  editarSugestao(): void {
    // TODO: Implementar modal de edição ou tornar textarea editável
    console.log('Modo edição ativado');
  }

  async regenerarSugestao(): Promise<void> {
    await this.gerarSugestao();
  }



  getFaseBadgeClass(fase: string): string {
    if (this.faseSpin === fase) {
      return fase === 'auto' ? 'btn btn-info' : 'btn btn-primary';
    }
    return 'btn btn-outline-secondary';
  }

  // Listener para receber mensagens do content script
  @HostListener('window:message', ['$event'])
  handleMessage(event: MessageEvent): void {
    console.log('[WhatsApp Assistant] Mensagem recebida no HostListener:', event.data);
    if (event.data) {
      // Receber informações do contato selecionado
      if (event.data.tipo === 'SELECIONOU_CONTATO' && event.data.payload) {
        const payload = event.data.payload;
        console.log('[WhatsApp Assistant] Recebido SELECIONOU_CONTATO:', payload);

        // Sempre armazenar o nome do WhatsApp
        if (payload.nome) {
          this.nomeWhatsApp = payload.nome;
          console.log('[WhatsApp Assistant] Nome do WhatsApp:', this.nomeWhatsApp);
        }
      }

      // Receber contexto da conversa com mensagens
      if (event.data.tipo === 'CONTEXTO_CONVERSA') {
        console.log('[WhatsApp Assistant] Recebido CONTEXTO_CONVERSA:', event.data);
        this.capturandoMensagens = false;

        // Capturar nome do contexto também
        if (event.data.nome) {
          this.nomeWhatsApp = event.data.nome;
          console.log('[WhatsApp Assistant] Nome do WhatsApp (contexto):', this.nomeWhatsApp);
        }

        if (event.data.mensagens && Array.isArray(event.data.mensagens)) {
          console.log('[WhatsApp Assistant] Mensagens recebidas para processar:', event.data.mensagens);

          // Processar mensagens para corrigir remetente e filtrar sem texto
          this.mensagensCapturadas = event.data.mensagens
            .filter((msg: any) => {
              // Filtrar mensagens sem texto logo na entrada
              const textoLimpo = (msg.texto || '').trim();
              if (!textoLimpo || textoLimpo === '[Mensagem sem texto]') {
                console.log('[WhatsApp Assistant] Ignorando mensagem sem texto na entrada:', msg);
                return false;
              }
              return true;
            })
            .map((msg: any, index: number) => {
            // Debug detalhado de cada mensagem
            console.log(`[WhatsApp Assistant] Processando mensagem ${index}:`, {
              remetenteOriginal: msg.remetente,
              fromMe: msg.fromMe,
              isFromMe: msg.isFromMe,
              isSentByMe: msg.isSentByMe,
              tipo: msg.tipo,
              texto: msg.texto?.substring(0, 50)
            });

            // Determinar remetente com múltiplas verificações
            let remetenteCorrigido = 'Lead'; // Padrão é lead

            // Verificar múltiplos indicadores de mensagem enviada
            if (msg.fromMe === true ||
                msg.isFromMe === true ||
                msg.isSentByMe === true ||
                msg.tipo === 'saida' ||
                msg.isOutgoing === true ||
                (msg.remetente === 'Eu')) {
              remetenteCorrigido = 'Eu';
            } else if (msg.remetente && msg.remetente !== 'Lead' &&
                       this.nomeWhatsApp && msg.remetente === this.nomeWhatsApp) {
              // Se o remetente é o mesmo nome do contato do WhatsApp, é do Lead
              remetenteCorrigido = 'Lead';
            }

            console.log(`[WhatsApp Assistant] Remetente corrigido: ${remetenteCorrigido}`);

            return {
              ...msg,
              remetente: remetenteCorrigido,
              // Manter o nome original para exibição se necessário
              nomeOriginal: msg.remetente
            };
          });

          console.log('[WhatsApp Assistant] Mensagens processadas:', this.mensagensCapturadas);

          // Criar resumo automático das mensagens
          this.criarResumoConversa();
        }
      }
    }
  }

  // Criar resumo da conversa baseado nas mensagens
  private criarResumoConversa(): void {
    if (this.mensagensCapturadas.length > 0) {
      const ultimasMensagens = this.mensagensCapturadas
        .slice(-3)
        .map(m => `${m.remetente}: ${m.texto}`)
        .join(' | ');

      this.contextoResumo = `Últimas mensagens: ${ultimasMensagens}`;
    }
  }

  // Solicitar mensagens atuais do WhatsApp
  private async solicitarMensagensAtuais(): Promise<void> {
    return new Promise((resolve) => {
      const requestId = Date.now();
      console.log('[WhatsApp Assistant] Solicitando mensagens atuais, requestId:', requestId);

      // Listener temporário para a resposta
      const handleResponse = (event: MessageEvent) => {
        console.log('[WhatsApp Assistant] Evento recebido em handleResponse:', event.data);
        if (event.data && event.data.tipo === 'MENSAGENS_ATUALIZADAS' && event.data.requestId === requestId) {
          console.log('[WhatsApp Assistant] Mensagens atualizadas recebidas:', event.data);

          if (event.data.mensagens && Array.isArray(event.data.mensagens)) {
            // Filtrar mensagens sem texto também aqui
            this.mensagensCapturadas = event.data.mensagens.filter((msg: any) => {
              const textoLimpo = (msg.texto || '').trim();
              return textoLimpo && textoLimpo !== '[Mensagem sem texto]';
            });
            this.criarResumoConversa();
          }

          // Remover o listener após receber a resposta
          window.removeEventListener('message', handleResponse);
          resolve();
        }
      };

      // Adicionar listener
      window.addEventListener('message', handleResponse);

      // Enviar solicitação
      console.log('[WhatsApp Assistant] Enviando solicitação de mensagens, requestId:', requestId);
      const message = {
        tipo: 'SOLICITAR_MENSAGENS_ATUAIS',
        requestId: requestId
      };

      // Usar window.parent.postMessage se estiver em iframe
      if (this.isInIframe) {
        console.log('[WhatsApp Assistant] Enviando para parent window (iframe mode)');
        window.parent.postMessage(message, '*');
      } else {
        console.log('[WhatsApp Assistant] Enviando para window (non-iframe mode)');
        window.postMessage(message, '*');
      }
      console.log('[WhatsApp Assistant] Solicitação enviada via postMessage');

      // Timeout de segurança (3 segundos)
      setTimeout(() => {
        window.removeEventListener('message', handleResponse);
        resolve();
      }, 3000);
    });
  }

  // Método para simular recebimento de mensagens (debug)
  simularMensagens(): void {
    const mensagensSimuladas = [
      {
        texto: 'Olá, gostaria de saber mais sobre o sistema',
        remetente: 'Lead',
        horario: '10:30:15',
        tipo: 'text'
      },
      {
        texto: 'Oi! Claro, posso te ajudar. O que você gostaria de saber?',
        remetente: 'Eu',
        horario: '10:31:20',
        tipo: 'text'
      },
      {
        texto: 'Estamos tendo problemas com controle de estoque',
        remetente: 'Lead',
        horario: '10:32:45',
        tipo: 'text'
      },
      {
        texto: 'Quantas lojas vocês possuem?',
        remetente: 'Eu',
        horario: '10:33:10',
        tipo: 'text'
      },
      {
        texto: 'Temos 3 lojas físicas',
        remetente: 'Lead',
        horario: '10:33:55',
        tipo: 'text'
      }
    ];

    this.mensagensCapturadas = mensagensSimuladas;
    this.capturandoMensagens = false;
    this.criarResumoConversa();
  }

  // Método para limpar todas as mensagens
  limparMensagens(): void {
    this.mensagensCapturadas = [];
    this.contextoResumo = '';
    this.capturandoMensagens = true;
  }

  // Detectar e salvar fase SPIN automaticamente
  detectarESalvarFase(): void {
    if (!this.telefone || this.mensagensCapturadas.length === 0) {
      alert('É necessário ter mensagens capturadas para detectar a fase.');
      return;
    }

    this.detectandoFase = true;

    this.sugestoesService.detectarESalvarFaseSpin(this.telefone, this.mensagensCapturadas)
      .subscribe({
        next: (resultado) => {
          this.faseDetectadaPelaIA = resultado.faseSpin;
          this.confiancaDeteccao = resultado.confianca;
          this.dataUltimaDeteccao = new Date();

          // Atualizar o lead local se encontrado
          if (resultado.leadEncontrado && this.contato.id) {
            this.contato.faseSpinDetectada = resultado.faseSpin;
            this.contato.confiancaFaseDetectada = resultado.confianca;
            this.contato.dataUltimaDeteccaoFase = new Date();
          }

          // Mostrar feedback para o usuário
          this.mostrarFeedback(
            `Fase detectada: ${this.getFaseDisplayName(resultado.faseSpin)} (${Math.round(resultado.confianca * 100)}% confiança)`,
            'success'
          );

          console.log('[WhatsApp Assistant] Fase detectada e salva:', resultado);
        },
        error: (erro) => {
          console.error('[WhatsApp Assistant] Erro ao detectar fase:', erro);
          this.mostrarFeedback('Erro ao detectar fase da conversa', 'error');
        },
        complete: () => {
          this.detectandoFase = false;
        }
      });
  }

  // Obter nome amigável da fase
  getFaseDisplayName(fase: string): string {
    const nomes: Record<string, string> = {
      'rapport': 'Rapport',
      'situacao': 'Situação',
      'problema': 'Problema',
      'implicacao': 'Implicação',
      'necessidade': 'Necessidade',
      'auto': 'Auto-detectar'
    };
    return nomes[fase] || fase;
  }

  // Mostrar feedback para o usuário
  private mostrarFeedback(mensagem: string, tipo: 'success' | 'error' | 'info' = 'info'): void {
    // Por enquanto usar console.log, depois pode implementar toast/alert
    console.log(`[${tipo.toUpperCase()}] ${mensagem}`);

    // Implementação simples com alert por enquanto
    if (tipo === 'error') {
      alert(`Erro: ${mensagem}`);
    } else if (tipo === 'success') {
      alert(`Sucesso: ${mensagem}`);
    }
  }

  // Mapear fase SPIN para etapa do funil
  private mapearFaseSpinParaEtapa(fase: string): string {
    const mapeamento: Record<string, string> = {
      'rapport': 'Rapport',
      'situacao': 'Prospecção',
      'problema': 'Qualificação',
      'implicacao': 'Objeção',
      'necessidade': 'Fechamento',
      'auto': 'auto' // Manter 'auto' para que o backend detecte
    };
    return mapeamento[fase] || 'Prospecção';
  }



  // Gerar rapport - método público para o botão
  async gerarRapport(): Promise<void> {
    this.enviarMensagem('Preciso de uma mensagem de rapport/atratividade para este lead de pizzaria.');
  }



  // Método removido - agora sempre usamos IA para gerar mensagens de rapport
  // private gerarRapportMockado(): string {
  //   const mensagensRapport = {
  //     direta: [
  //       `Oi ${this.contato.nome || 'você'}! 👋 Vi que sua empresa está crescendo - isso é ótimo! Tenho uma solução que pode ajudar a escalar ainda mais rápido. Posso te mostrar em apenas 5 minutos?`,
  //       `${this.contato.nome || 'Olá'}, notei que empresas como a sua estão economizando até 3h por dia com nossa solução. Que tal descobrir como fazer o mesmo? 🚀`,
  //       `Oi ${this.contato.nome || 'você'}! Separei 3 cases de sucesso de empresas similares à sua. Vale a pena dar uma olhada - pode ser um divisor de águas! Posso enviar?`
  //     ],
  //     indireta: [
  //       `Oi ${this.contato.nome || 'você'}! Como estão as coisas por aí? 😊 Vi algumas novidades interessantes no mercado de ${this.contato.empresa ? 'seu segmento' : 'gestão'} que podem te interessar...`,
  //       `${this.contato.nome || 'Olá'}, espero que esteja tendo um ótimo dia! 🌟 Estava pensando em alguns desafios comuns que empresas enfrentam nessa época do ano. Como vocês estão lidando com isso?`,
  //       `Oi! Acabei de ler um artigo sobre tendências em ${this.contato.empresa ? 'seu mercado' : 'gestão empresarial'} e lembrei de você. Algumas insights bem interessantes! Gostaria de compartilhar?`
  //     ],
  //     consultiva: [
  //       `${this.contato.nome || 'Olá'}, percebi que muitas empresas do seu porte estão buscando formas de otimizar processos. Qual tem sido seu maior desafio operacional atualmente?`,
  //       `Oi ${this.contato.nome || 'você'}! Tenho ajudado empresas a resolver questões específicas de gestão. Curioso(a) para saber: qual área da sua operação consome mais tempo hoje?`,
  //       `${this.contato.nome || 'Olá'}, uma pergunta rápida: se pudesse resolver apenas UM problema na sua empresa hoje, qual seria? Talvez eu possa ajudar com algumas ideias...`
  //     ]
  //   };

  //   const mensagens = mensagensRapport[this.tipoAbordagem];
  //   return mensagens[Math.floor(Math.random() * mensagens.length)];
  // }

  // Gerar mensagem de apresentação
  async gerarMensagemApresentacao(): Promise<void> {
    try {
      console.log('[WhatsApp Assistant] Gerando mensagem de apresentação profissional');

      const contextoApresentacao = {
        telefone: this.telefone,
        nomeContato: this.contato.nome || this.nomeWhatsApp,
        empresa: this.contato.empresa,
        produto: this.produto,
        lead: this.contato
      };

      // Chamar serviço para gerar mensagens de apresentação
      this.sugestoesService.gerarMensagemApresentacao(contextoApresentacao).subscribe(
        (sugestoes) => {
          console.log('[WhatsApp Assistant] Resposta de apresentação recebida:', sugestoes);

          if (sugestoes && sugestoes.length > 0) {
            // Sugestões geradas com sucesso
            this.faseDetectadaAutomaticamente = 'Modo Apresentação - Primeira abordagem profissional';
            console.log('[WhatsApp Assistant] Mensagens de apresentação geradas com sucesso');
          } else {
            // Mostrar erro se não conseguiu gerar sugestões
            this.erro = 'Não foi possível gerar mensagem de apresentação. Por favor, tente novamente.';
          }
          this.carregando = false;
        },
        (erro) => {
          console.error('Erro ao gerar mensagem de apresentação:', erro);
          this.erro = 'Erro ao conectar com o servidor de IA. Por favor, tente novamente.';
          this.carregando = false;
        }
      );
    } catch (error) {
      console.error('Erro ao gerar mensagem de apresentação:', error);
      this.erro = 'Erro ao gerar mensagem. Tente novamente.';
      this.carregando = false;
    }
  }


  // Toggle modo apresentação
  toggleModoApresentacao(): void {
    this.modoApresentacao = !this.modoApresentacao;
  }

  // Mudar tipo de abordagem
  mudarTipoAbordagem(tipo: 'direta' | 'indireta' | 'consultiva'): void {
    this.tipoAbordagem = tipo;
  }

  // Cadastrar lead rapidamente
  async cadastrarLeadRapido(): Promise<void> {
    if (!this.telefone) {
      this.mostrarFeedback('Telefone é obrigatório', 'error');
      return;
    }

    const novoLead = {
      nomeResponsavel: this.nomeWhatsApp || 'Contato WhatsApp',
      empresa: this.novoLead.empresa || `Cliente ${this.telefone}`,
      telefone: this.telefone,
      email: this.novoLead.email,
      observacoes: this.novoLead.observacoes,
      origem: 'WhatsApp',
      crmEmpresaId: 1 // TODO: Pegar o ID da empresa corrente
    };

    try {
      console.log('[WhatsApp Assistant] Cadastrando novo lead:', novoLead);
      const response = await this.leadService.salveLead(novoLead);

      // Se chegou aqui, o lead foi salvo com sucesso (response é o lead salvo)
      this.mostrarFeedback('Lead cadastrado com sucesso!', 'success');

      // Recarregar dados do contato
      this.carregarDadosContato();

      // Limpar formulário
      this.novoLead = {
        empresa: '',
        email: '',
        observacoes: ''
      };
    } catch (erro) {
      console.error('Erro ao cadastrar lead:', erro);
      this.mostrarFeedback('Erro ao cadastrar lead', 'error');
    }
  }

  // Método para detectar se está rodando em iframe
  private inIframe(): boolean {
    try {
      return window.self !== window.top;
    } catch (e) {
      return true;
    }
  }

  // Obter informações do concorrente
  getConcorrenteInfo(nomeConcorrente: string): any {
    const concorrentes: Record<string, any> = {
      'iFood': {
        pontosFracos: [
          'Taxa de 27% sobre vendas',
          'Não tem sistema de fidelidade próprio',
          'Cliente não consegue contato direto',
          'Demora para receber pagamentos'
        ],
        diferenciais: [
          'Taxa única de R$ 89/mês',
          'Sistema de fidelidade integrado',
          'WhatsApp direto com cliente',
          'Recebe na hora via Pix'
        ],
        sugestaoAbordagem: 'Foque no custo-benefício e no relacionamento direto com cliente'
      },
      'Rappi': {
        pontosFracos: [
          'Taxa alta e variável',
          'Interface complexa para gerenciar',
          'Suporte limitado',
          'Não permite personalização'
        ],
        diferenciais: [
          'Interface simples e intuitiva',
          'Suporte humanizado via WhatsApp',
          'Totalmente personalizável',
          'Sem taxas variáveis'
        ],
        sugestaoAbordagem: 'Destaque a simplicidade e o suporte humanizado'
      },
      'Goomer': {
        pontosFracos: [
          'Focado apenas em cardápio',
          'Não tem delivery integrado',
          'Precisa de outros sistemas',
          'Custo adicional por funcionalidade'
        ],
        diferenciais: [
          'Sistema completo all-in-one',
          'Delivery próprio integrado',
          'Tudo em uma única plataforma',
          'Preço único com tudo incluído'
        ],
        sugestaoAbordagem: 'Mostre como ter tudo integrado economiza tempo e dinheiro'
      },
      'Aiqfome': {
        pontosFracos: [
          'Taxa sobre vendas',
          'Limitado a algumas cidades',
          'Sistema básico',
          'Pouca customização'
        ],
        diferenciais: [
          'Funciona em qualquer cidade',
          'Sistema completo e robusto',
          'Altamente customizável',
          'Sem taxa sobre vendas'
        ],
        sugestaoAbordagem: 'Enfatize a liberdade geográfica e ausência de taxas'
      },
      'default': {
        pontosFracos: [
          'Sistema genérico',
          'Suporte limitado',
          'Custos adicionais',
          'Pouca integração'
        ],
        diferenciais: [
          'Feito para restaurantes brasileiros',
          'Suporte 24/7 humanizado',
          'Tudo incluído no plano',
          'Integração total'
        ],
        sugestaoAbordagem: 'Pergunte sobre as dificuldades atuais e mostre como resolvemos'
      }
    };

    // Retorna info específica ou default
    return concorrentes[nomeConcorrente] || concorrentes['default'];
  }

  // Método para obter iniciais do nome para o avatar
  getInitials(nome: string): string {
    if (!nome) return 'NC';

    const nomes = nome.trim().split(' ').filter(n => n.length > 0);
    if (nomes.length === 0) return 'NC';

    if (nomes.length === 1) {
      return nomes[0].substring(0, 2).toUpperCase();
    }

    return (nomes[0].charAt(0) + nomes[nomes.length - 1].charAt(0)).toUpperCase();
  }

  // Método para obter ícone do link baseado no tipo
  getLinkIcon(tipo: string): string {
    const icones: { [key: string]: string } = {
      'site': 'fe-globe',
      'website': 'fe-globe',
      'instagram': 'fe-instagram',
      'facebook': 'fe-facebook',
      'ifood': 'fe-shopping-bag',
      'ubereats': 'fe-truck',
      'whatsapp': 'fe-message-circle',
      'linkedin': 'fe-linkedin',
      'youtube': 'fe-youtube',
      'twitter': 'fe-twitter',
      'delivery': 'fe-truck',
      'marketplace': 'fe-shopping-cart',
      'telefone': 'fe-phone',
      'email': 'fe-mail',
      'localizacao': 'fe-map-pin',
      'sitecardapio': 'fe-menu',
      'cardapio': 'fe-menu',
      'reservas': 'fe-calendar',
      'concorrente': 'fe-external-link',
      'default': 'fe-link'
    };

    return icones[tipo.toLowerCase()] || icones['default'];
  }

  // Método para obter classe do ícone do link
  getLinkIconClass(tipo: string): string {
    const classes: { [key: string]: string } = {
      'site': 'website',
      'website': 'website',
      'instagram': 'instagram',
      'ifood': 'ifood',
      'whatsapp': 'whatsapp',
      'localizacao': 'localizacao',
      'sitecardapio': 'cardapio',
      'cardapio': 'cardapio',
      'reservas': 'reservas',
      'concorrente': 'concorrente'
    };

    return classes[tipo.toLowerCase()] || 'default';
  }

  // Método para obter label do tipo de link
  getLinkLabel(tipo: string): string {
    const labels: { [key: string]: string } = {
      'site': 'Website',
      'website': 'Website',
      'instagram': 'Instagram',
      'ifood': 'iFood',
      'whatsapp': 'WhatsApp',
      'localizacao': 'Localização',
      'sitecardapio': 'Cardápio Digital',
      'cardapio': 'Cardápio',
      'reservas': 'Reservas',
      'concorrente': 'Sistema Atual'
    };

    return labels[tipo.toLowerCase()] || tipo;
  }

  // Método para formatar texto de exibição do link
  getLinkDisplayText(url: string): string {
    if (!url) return '';

    // Remove protocolo para exibição mais limpa
    let display = url.replace(/^https?:\/\//, '');

    // Remove www.
    display = display.replace(/^www\./, '');

    // Limita o tamanho se muito longo
    if (display.length > 30) {
      display = display.substring(0, 27) + '...';
    }

    return display;
  }

  // Método para verificar se uma etapa SPIN foi completada
  isStepCompleted(step: string): boolean {
    const stepOrder = ['situacao', 'problema', 'implicacao', 'necessidade'];
    const currentIndex = stepOrder.indexOf(this.faseSpin);
    const stepIndex = stepOrder.indexOf(step);

    // Etapa é completada se já passou dela na sequência
    return currentIndex > stepIndex;
  }

  // Método para gerar URL do Bitrix24
  getBitrixUrl(): string {
    if (!this.contato.idBitrix) return '';
    return `https://b24-chlbsw.bitrix24.com.br/crm/lead/details/${this.contato.idBitrix}/`;
  }

  // Métodos para o chat
  enviarMensagem(mensagemDireta?: string): void {
    let mensagem: string;

    if (mensagemDireta) {
      // Mensagem passada diretamente (ex: botões rápidos)
      mensagem = mensagemDireta;
    } else {
      // Mensagem do input do usuário
      if (!this.mensagemUsuario?.trim() || this.carregando) return;
      mensagem = this.mensagemUsuario.trim();
      this.mensagemUsuario = '';
    }

    // Processar a mensagem do usuário e gerar resposta
    this.processarMensagemUsuario(mensagem);
  }

  private async processarMensagemUsuario(mensagem: string): Promise<void> {
    this.carregando = true;
    this.erro = null;

    try {
      // Adicionar mensagem do SDR ao histórico
      this.historicoConversaAgente.push({
        role: 'user',
        content: mensagem,
        timestamp: new Date()
      });

      // Fazer scroll para mostrar nova mensagem do usuário
      this.forcarScrollParaFinal();

      // Preparar contexto para conversa com agente
      const contexto = {
        telefone: this.telefone,
        mensagens: this.mensagensCapturadas,
        etapaFunil: this.mapearFaseSpinParaEtapa(this.faseSpin),
        tomConversa: this.tipoTom === 'formal' ? 'Consultivo' :
                     this.tipoTom === 'informal' ? 'Empático' : 'Técnico',
        produto: this.produto,
        contato: this.contato // Adicionar dados do contato
      };

      // Conversar com agente IA incluindo histórico
      console.log('[WhatsApp Assistant] Conversando com agente:', {
        mensagem,
        contexto,
        historico: this.historicoConversaAgente,
        contatoNome: this.contato?.nomeResponsavel || this.contato?.nome || 'Não informado',
        contatoEmpresa: this.contato?.empresa || 'Não informada',
        contatoTelefone: this.contato?.telefone || 'Não informado'
      });
      this.sugestoesService.conversarComAgente(contexto, mensagem, this.historicoConversaAgente).subscribe(
        (resposta: any) => {
          console.log('[WhatsApp Assistant] Resposta do agente recebida:', resposta);

          // Adicionar resposta do agente ao histórico
          this.historicoConversaAgente.push({
            role: 'assistant',
            content: resposta.resposta,
            timestamp: new Date()
          });

          // Manter apenas últimas 20 mensagens do histórico (10 pares pergunta-resposta)
          if (this.historicoConversaAgente.length > 20) {
            this.historicoConversaAgente = this.historicoConversaAgente.slice(-20);
            // Scroll adicional após modificar o array (pode afetar o DOM)
            setTimeout(() => this.scrollToBottom(), 50);
          }

          // Resposta do agente processada com sucesso
          console.log('[WhatsApp Assistant] Resposta do agente adicionada ao histórico');

          // Atualizar fase se sugerida
          if (resposta.faseSugerida) {
            this.faseSpin = resposta.faseSugerida as 'rapport' | 'situacao' | 'problema' | 'implicacao' | 'necessidade' | 'auto';
            if (resposta.observacoes) {
              this.faseDetectadaAutomaticamente = resposta.observacoes;
            }
          }

          // Fazer scroll para mostrar nova mensagem (múltiplas tentativas)
          this.forcarScrollParaFinal();

          this.carregando = false;
        },
        (erro) => {
          console.error('Erro ao conversar com agente:', erro);
          this.erro = 'Erro ao processar sua mensagem. Tente novamente.';
          this.carregando = false;
        }
      );
    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
      this.erro = 'Erro ao processar sua mensagem. Tente novamente.';
      this.carregando = false;
    }
  }



  // Método para visualizar histórico da conversa (debug)
  visualizarHistoricoConversa(): void {
    console.log('[WhatsApp Assistant] Histórico da conversa SDR-Agente:', this.historicoConversaAgente);
    console.log('[WhatsApp Assistant] Total de mensagens no histórico:', this.historicoConversaAgente.length);
  }

  // Método para limpar histórico manualmente
  limparHistoricoConversa(): void {
    this.historicoConversaAgente = [];
    console.log('[WhatsApp Assistant] Histórico da conversa limpo');

    // Adicionar mensagem inicial após limpar
    this.adicionarMensagemInicialAgente();

    // Scroll para mostrar a mensagem inicial
    setTimeout(() => this.scrollToBottom(), 100);
  }

  // Método para copiar texto para clipboard
  copiarTexto(texto: string): void {
    // Extrair apenas o conteúdo entre aspas duplas, se existir
    const match = texto.match(/"([^"]+)"/);
    const textoParaCopiar = match ? match[1] : texto;

    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(textoParaCopiar).then(() => {
        console.log('[WhatsApp Assistant] Texto copiado para clipboard');
        // Aqui você pode adicionar uma notificação visual se quiser
      }).catch(err => {
        console.error('[WhatsApp Assistant] Erro ao copiar texto:', err);
        this.copiarTextoFallback(textoParaCopiar);
      });
    } else {
      this.copiarTextoFallback(textoParaCopiar);
    }
  }

  // Método para verificar se o texto contém aspas
  temAspas(texto: string): boolean {
    return texto && texto.includes('"') && texto.match(/"([^"]+)"/) !== null;
  }



  // Método para fazer scroll automático para o final da conversa
  private scrollToBottom(): void {
    try {
      // Método 1: Scroll da área principal do chat
      const chatArea = document.querySelector('.chat-area');
      if (chatArea) {
        chatArea.scrollTop = chatArea.scrollHeight;
        console.log('[WhatsApp Assistant] Scroll realizado para o final do chat');

        // Scroll adicional para garantir que chegou ao final
        setTimeout(() => {
          chatArea.scrollTop = chatArea.scrollHeight;
        }, 50);
        return;
      }

      // Método 2: Usar scrollIntoView na última mensagem
      const lastMessage = document.querySelector('.chat-message-wrapper:last-child');
      if (lastMessage) {
        lastMessage.scrollIntoView({ behavior: 'smooth', block: 'end' });
        console.log('[WhatsApp Assistant] Scroll realizado usando scrollIntoView');
        return;
      }

      // Método 3: Fallback para container principal
      const mainContainer = document.querySelector('.whatsapp-assistant-container');
      if (mainContainer) {
        mainContainer.scrollTop = mainContainer.scrollHeight;
        console.log('[WhatsApp Assistant] Scroll realizado no container principal');
        return;
      }

      console.warn('[WhatsApp Assistant] Nenhum elemento encontrado para fazer scroll');
    } catch (err) {
      console.error('[WhatsApp Assistant] Erro ao fazer scroll:', err);
    }
  }

  // Método para forçar scroll com múltiplas tentativas (usado após inserir mensagens)
  private forcarScrollParaFinal(): void {
    // Primeira tentativa imediata
    setTimeout(() => this.scrollToBottom(), 50);

    // Segunda tentativa após um pouco mais de tempo
    setTimeout(() => this.scrollToBottom(), 150);

    // Terceira tentativa para garantir (especialmente útil em dispositivos lentos)
    setTimeout(() => this.scrollToBottom(), 300);
  }

  gerarSugestaoEspecifica(tipo: string): void {
    const mensagens = {
      'spin-situacao': 'Preciso de uma mensagem SPIN para descobrir a situação atual do lead.',
      'objecao-dinheiro': 'Como posso responder a uma objeção sobre preço/dinheiro?',
      'agendar-demo': 'Preciso de uma mensagem para agendar uma demonstração.',
      'follow-up': 'Como fazer um follow-up efetivo com este lead?',
      'vamos-pensar': 'Como responder quando o lead diz "vamos pensar"?',
      'lanchonete': 'Preciso de uma abordagem específica para lanchonetes.',
      'bar-boteco': 'Como abordar especificamente bares e botecos?'
    };

    const mensagem = mensagens[tipo] || `Preciso de uma sugestão para: ${tipo}`;
    this.enviarMensagem(mensagem);
  }

  analisarWhatsApp(): void {
    // Implementar análise do WhatsApp
    console.log('Analisando WhatsApp...');
    this.gerarSugestao();
  }

  // Verificar se o lead está mal cadastrado
  isLeadMalCadastrado(): boolean {
    return this.getProblemasLead().length > 0;
  }

  // Obter lista de problemas do lead
  getProblemasLead(): Array<{tipo: string, descricao: string}> {
    const problemas: Array<{tipo: string, descricao: string}> = [];

    // PRIORIDADE 1: Verificar se o lead não está cadastrado no Bitrix
    if (!this.contato.id) {
      problemas.push({
        tipo: 'Lead não cadastrado',
        descricao: 'Este contato não está cadastrado no Bitrix24. Cadastre para ter acesso completo aos dados e histórico.'
      });
      // Se não está cadastrado, não precisa verificar outros problemas
      return problemas;
    }

    // Verificar nome genérico
    if (this.isNomeGenerico()) {
      problemas.push({
        tipo: 'Nome genérico',
        descricao: `"${this.contato.nomeResponsavel}" não é um nome pessoal. Use o nome real da pessoa responsável.`
      });
    }

    // Verificar CNPJ ausente
    if (this.isCnpjAusente()) {
      problemas.push({
        tipo: 'CNPJ ausente',
        descricao: 'CNPJ não informado. Adicione o CNPJ da empresa para validação e credibilidade.'
      });
    }

    // Verificar CNPJ inválido
    if (this.isCnpjInvalido()) {
      problemas.push({
        tipo: 'CNPJ inválido',
        descricao: `CNPJ "${this.contato.cnpj}" está em formato inválido. Verifique e corrija.`
      });
    }

    return problemas;
  }

  // Verificar se o nome é genérico
  private isNomeGenerico(): boolean {
    if (!this.contato.nomeResponsavel) return false;

    const nomeGenerico = this.contato.nomeResponsavel.toLowerCase().trim();
    const nomesGenericos = [
      'proprietário',
      'proprietario',
      'dono',
      'dona',
      'gerente',
      'responsável',
      'responsavel',
      'administrador',
      'admin',
      'sócio',
      'socio',
      'diretor',
      'diretora',
      'chefe',
      'coordenador',
      'coordenadora',
      'supervisor',
      'supervisora',
      'funcionário',
      'funcionario',
      'atendente',
      'vendedor',
      'vendedora',
      'balconista',
      'caixa',
      'sr',
      'sra',
      'senhor',
      'senhora'
    ];

    return nomesGenericos.some(nome => nomeGenerico.includes(nome));
  }

  // Verificar se CNPJ está ausente
  private isCnpjAusente(): boolean {
    return !this.contato.cnpj || this.contato.cnpj.trim() === '';
  }

  // Verificar se CNPJ é inválido
  private isCnpjInvalido(): boolean {
    if (!this.contato.cnpj) return false;

    const cnpj = this.contato.cnpj.replace(/[^\d]/g, ''); // Remove caracteres não numéricos

    // CNPJ deve ter 14 dígitos
    if (cnpj.length !== 14) return true;

    // Verificar se não são todos os dígitos iguais
    if (/^(\d)\1{13}$/.test(cnpj)) return true;

    // Validação básica dos dígitos verificadores
    return !this.validarCnpj(cnpj);
  }

  // Validar CNPJ (algoritmo oficial)
  private validarCnpj(cnpj: string): boolean {
    const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];

    // Primeiro dígito verificador
    let sum = 0;
    for (let i = 0; i < 12; i++) {
      sum += parseInt(cnpj[i]) * weights1[i];
    }
    let remainder = sum % 11;
    const digit1 = remainder < 2 ? 0 : 11 - remainder;

    if (parseInt(cnpj[12]) !== digit1) return false;

    // Segundo dígito verificador
    sum = 0;
    for (let i = 0; i < 13; i++) {
      sum += parseInt(cnpj[i]) * weights2[i];
    }
    remainder = sum % 11;
    const digit2 = remainder < 2 ? 0 : 11 - remainder;

    return parseInt(cnpj[13]) === digit2;
  }

  // Função para ajustar telefone do WhatsApp (adicionar 9º dígito se necessário)
  private ajusteTelefoneWhatsapp(telefone: string): string {
    if (!telefone) return telefone;

    // Telefone específico que não deve ser alterado
    if (telefone.indexOf('8431131968') !== -1) {
      return telefone;
    }

    // Se começar com 55, tiver menos de 13 dígitos e não for linha fixa
    if (telefone.indexOf("55") === 0 && telefone.length < 13) {
      // Verificar se é celular (não linha fixa) - simplificado
      // Códigos de área que indicam celular: 11, 21, 31, 41, 51, 61, 71, 81, 85, etc.
      const codigoArea = telefone.substr(2, 2);
      const primeiroDigito = telefone.substr(4, 1);

      // Se o primeiro dígito após o código de área não é 9 e não é linha fixa (2, 3, 4, 5)
      if (primeiroDigito !== '9' && !['2', '3', '4', '5'].includes(primeiroDigito)) {
        // Adicionar o 9º dígito
        telefone = telefone.substr(0, 4) + '9' + telefone.substr(4);
      }
    }

    return telefone;
  }

  // Método para formatar telefone corretamente (fixo e celular)
  private formatarTelefoneBrasileiro(telefone: string): string {
    if (!telefone) return '';

    // Remove tudo que não é dígito
    const digits = telefone.replace(/\D/g, '');

    if (digits.length === 10) {
      // Telefone fixo: (XX) XXXX-XXXX
      return `(${digits.substr(0, 2)}) ${digits.substr(2, 4)}-${digits.substr(6, 4)}`;
    } else if (digits.length === 11) {
      // Telefone celular: (XX) XXXXX-XXXX
      return `(${digits.substr(0, 2)}) ${digits.substr(2, 5)}-${digits.substr(7, 4)}`;
    } else {
      // Formato não reconhecido, retorna como está
      return telefone;
    }
  }

  // Método para formatar texto convertendo \n em <br>
  formatarTextoComQuebras(texto: string): string {
    if (!texto) return '';

    // Converter \n em <br> e escapar HTML para segurança
    return texto
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
      .replace(/\n/g, '<br>');
  }

  // Método para remover código do país e formatar telefone
  formatarTelefone(telefone: string): string {
    if (!telefone) return '';

    // Primeiro, limpar e ajustar o telefone
    let telefoneClean = telefone.toString().replace(/\D/g, ''); // Remove tudo que não é dígito

    // Aplicar ajuste do WhatsApp antes de remover código do país
    telefoneClean = this.ajusteTelefoneWhatsapp(telefoneClean);

    // Se começar com 55 e tiver mais de 11 dígitos, remover o 55
    if (telefoneClean.startsWith('55') && telefoneClean.length > 11) {
      telefoneClean = telefoneClean.substring(2);
    }

    // Usar nossa formatação correta ao invés do pipe
    return this.formatarTelefoneBrasileiro(telefoneClean);
  }

  // Abrir cadastro no Bitrix para lead não cadastrado
  abrirCadastroBitrix(): void {
    console.log('Abrindo cadastro no Bitrix para lead não cadastrado:', this.telefone);

    const telefoneFormatado = this.formatarTelefone(this.telefone);

    let instrucoes = '📋 LEAD NÃO CADASTRADO NO BITRIX24\n\n';
    instrucoes += `Telefone: ${telefoneFormatado}\n`;
    instrucoes += `Nome WhatsApp: ${this.nomeWhatsApp || 'Não capturado'}\n\n`;
    instrucoes += '🔧 COMO CADASTRAR:\n\n';
    instrucoes += '1. Acesse o Bitrix24: https://b24-chlbsw.bitrix24.com.br/\n';
    instrucoes += '2. Vá em CRM > Leads > Adicionar Lead\n';
    instrucoes += '3. Preencha os dados obrigatórios:\n';
    instrucoes += `   • Nome: [Nome real da pessoa]\n`;
    instrucoes += `   • Empresa: [Nome da empresa]\n`;
    instrucoes += `   • Telefone: ${telefoneFormatado}\n`;
    instrucoes += '   • CNPJ: [CNPJ da empresa]\n';
    instrucoes += '   • Email: [Email de contato]\n\n';
    instrucoes += '4. Salve o lead\n';
    instrucoes += '5. Atualize esta página para carregar os dados\n\n';
    instrucoes += '💡 DICA: Cadastrar o lead permite:\n';
    instrucoes += '• Histórico completo de interações\n';
    instrucoes += '• Dados da empresa para personalização\n';
    instrucoes += '• Melhor qualificação e follow-up\n';
    instrucoes += '• Relatórios e métricas de vendas';

    alert(instrucoes);
  }

  // Abrir edição do lead
  abrirEdicaoLead(): void {
    // Implementar abertura de modal ou navegação para edição
    console.log('Abrindo edição do lead:', this.contato);

    const problemas = this.getProblemasLead();
    let instrucoes = 'Para corrigir o cadastro do lead:\n\n';
    instrucoes += '1. Acesse o CRM/Bitrix24\n';
    instrucoes += `2. Localize o lead: ${this.contato.nomeResponsavel || this.contato.empresa}\n`;
    instrucoes += '3. Corrija os seguintes problemas:\n\n';

    problemas.forEach((problema, index) => {
      instrucoes += `   ${index + 1}. ${problema.tipo}: ${problema.descricao}\n`;
    });

    instrucoes += '\n4. Salve as alterações\n\n';
    instrucoes += '✅ Isso melhorará significativamente a personalização e efetividade das mensagens de vendas!';

    // Por enquanto, mostrar um alert com instruções
    alert(instrucoes);
  }
}
