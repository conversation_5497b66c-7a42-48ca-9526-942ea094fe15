// Teste das tools do assistente WhatsApp
const { CrmWhatsappAssistantService } = require('./distServer/service/crm/CrmWhatsappAssistantService');

async function testarTools() {
    console.log('🚀 Testando sistema de tools do assistente WhatsApp...\n');
    
    const service = new CrmWhatsappAssistantService();
    
    // Simular um lead que menciona concorrente
    const params = {
        telefone: '11999999999',
        mensagens: [
            {
                remetente: 'Cliente',
                texto: 'Olá! Usamos iFood mas as taxas estão muito altas. Vocês têm algo melhor?',
                horario: '14:30'
            }
        ],
        faseSpin: 'problema',
        produto: 'Meu <PERSON>',
        tomConversa: 'informal',
        contato: {
            nomeResponsavel: '<PERSON> Silva',
            empresa: 'Pizzaria do Centro',
            telefone: '11999999999',
            concorrente: 'iFood',
            instagramHandle: '@pizzariadocentro'
        }
    };
    
    try {
        console.log('📝 Simulando conversa onde lead menciona iFood...');
        console.log('💬 Mensagem do lead:', params.mensagens[0].texto);
        console.log('\n⏳ Gerando sugestão...\n');
        
        const resultado = await service.gerarSugestaoResposta(params);
        
        console.log('✅ Resultado:', JSON.stringify(resultado, null, 2));
        
        if (resultado.observacoes && resultado.observacoes.includes('tools')) {
            console.log('\n🎯 Sistema de tools funcionando!');
        } else {
            console.log('\n⚠️  Tools podem não ter sido utilizadas');
        }
        
    } catch (error) {
        console.error('❌ Erro no teste:', error.message);
    }
}

// Executar teste
testarTools().then(() => {
    console.log('\n✅ Teste concluído!');
}).catch(error => {
    console.error('❌ Erro geral:', error);
});